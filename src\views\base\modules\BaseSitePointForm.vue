<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入站点编码"  disabled=""></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车道号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lane">
              <a-input v-model="model.lane" placeholder="请输入车道号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车道方向" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="direction">
              <a-input v-model="model.direction" placeholder="请输入车道方向"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="磅型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="poundType">
              <j-dict-select-tag  v-model="model.poundType" dictCode="pound_type" placeholder="请输入磅型" @change="getType"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="超载率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overRate">
              <a-input-number v-model="model.overRate" placeholder="请输入超载率" style="width: 100%"   :disabled="overRateDisabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseSitePointForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        overRateDisabled:false,
        overRate:"",
        url: {
          add: "/system/baseSitePoint/add",
          edit: "/system/baseSitePoint/edit",
          queryById: "/system/baseSitePoint/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add (siteCode) {
        this.model.code = siteCode;
        this.edit(this.model);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      getType(type){
          if(type == "1")
          {
            //选择动态磅
            this.model.overRate = this.overRate;
          }else{
            //选择静态磅
            this.overRate = this.model.overRate;
            this.model.overRate = 0;
            this.overRateDisabled = true;
          }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>