<template>
  <div id="data-view" >
    <dv-full-screen-container>
      <top-header></top-header>
      <div class="main-content">
        <div class="left">
          <data-stat class="left_top"></data-stat>
          <highway-chart class="left_center"></highway-chart>
          <enterprise-chart class="left_center"></enterprise-chart>
          <!-- <black-chart class="left_bottom"></black-chart> -->
        </div>
        <div class="center">
          <number-show class="center_top" :siteNum="siteNum" :onlineSiteNum="onlineSiteNum" :enterpriseNum="enterpriseNum"
            :onlineEnterpriseNum="onlineEnterpriseNum" :noSiteNum="noSiteNum" :onlineNoSiteNum="onlineNoSiteNum"
            :coalEnterpriseNum="coalEnterpriseNum" :onlineCoalEnterpriseNum="onlineCoalEnterpriseNum"></number-show>
          <center-map class="center_center"></center-map>
          <over-data-table class="center_bottom"></over-data-table>
        </div>
        <div class="right">
          <axle-chart class="right_top"></axle-chart>
          <site-chart class="right_center"></site-chart>
          <county-chart class="right_bottom"></county-chart>
        </div>
      </div>
    </dv-full-screen-container>
  </div>
</template>

<script>
import TopHeader from '@/views/bigscreen/modules/TopHeader'
import HighwayChart from "@/views/bigscreen/modules/HighwayChart"
import CenterMap from '@/views/bigscreen/modules/CenterMap'
import NumberShow from '@/views/bigscreen/modules/NumberShow'
import DataStat from '@/views/bigscreen/modules/DataStat'
import EnterpriseChart from '@/views/bigscreen/modules/EnterpriseChart'
import CountyChart from '@/views/bigscreen/modules/CountyChart'
import AxleChart from '@/views/bigscreen/modules/AxleChart'
import OverDataTable from '@/views/bigscreen/modules/OverDataTable'
import BlackChart from '@/views/bigscreen/modules/BlackChart'
import SiteChart from '@/views/bigscreen/modules/SiteChart'
import { getAction } from '@/api/manage'
import DevicePixelRatio from '@/utils/rem'
export default {
  name: 'BigScreen',
  components: {
    TopHeader,
    HighwayChart,
    CenterMap,
    NumberShow,
    DataStat,
    EnterpriseChart,
    CountyChart,
    AxleChart,
    OverDataTable,
    BlackChart,
    SiteChart,
    DevicePixelRatio
  },
  data() {
    return {
      url: {
        siteNumShowUrl: "/bigScreen/siteInfo",
        enterpriseNumShowUrl: "/bigScreen/enterpriseInfo",
        noSiteNumShowUrl: "/bigScreen/noSiteInfo",
        allNumUrl: "/bigScreen/numInfoNew"
      },
      siteNum: 0,
      onlineSiteNum: 0,
      enterpriseNum: 0,
      onlineEnterpriseNum: 0,
      coalEnterpriseNum: 0,
      onlineCoalEnterpriseNum: 0,
      noSiteNum: 0,
      onlineNoSiteNum: 0,
      timer: null
    }
  },
  created() {
    // this.siteShow();
    // this.enterpriseShow();
    // this.noSiteShow();
    this.allShow();
    this.DevicePixelRatio.init();
  },
  mounted() {
    // 每隔5分钟定时刷新
    this.timer = setInterval(() => {
      // this.siteShow();
      // this.enterpriseShow();
      // this.noSiteShow();
    }, 300000)

  },
  beforeDestroy() {
    clearTimeout(this.timer)
  },
  methods: {
    DevicePixelRatio,
    allShow() {
      getAction(this.url.allNumUrl).then((res) => {
        if (res.success) {
          console.log(res);
          this.siteNum = res.result.siteNumInfoVo.allNum;
          this.onlineSiteNum = res.result.siteNumInfoVo.onlineNum;
          this.enterpriseNum = res.result.enterpriseNumInfoVo.allNum;
          this.onlineEnterpriseNum = res.result.enterpriseNumInfoVo.onlineNum;
          // this.coalEnterpriseNum = res.result.coalEnterpriseNumInfoVo.allNum;
          // this.onlineCoalEnterpriseNum = res.result.coalEnterpriseNumInfoVo.onlineNum;
          this.noSiteNum = res.result.noSiteNumInfoVo.allNum;
          this.onlineNoSiteNum = res.result.noSiteNumInfoVo.onlineNum;
        }
      });
    },
    siteShow() {
      getAction(this.url.siteNumShowUrl).then((res) => {
        this.siteNum = res.result.allNum;
        this.onlineSiteNum = res.result.onlineNum;
      });
    },
    enterpriseShow() {
      getAction(this.url.enterpriseNumShowUrl).then((res) => {
        this.enterpriseNum = res.result.allNum;
        this.onlineEnterpriseNum = res.result.onlineNum;
      });
    },
    noSiteShow() {
      getAction(this.url.noSiteNumShowUrl).then((res) => {
        this.noSiteNum = res.result.allNum;
        this.onlineNoSiteNum = res.result.onlineNum;
      });
    },
  }


}
</script>

<style lang="less" scoped>
#data-view {
  width: 100%;
  height: 100vh;
  background-color: #030409;
  color: #fff;

  #dv-full-screen-container {
    background-image: url(~@/assets/screen/bg.jpg);
    background-size: 100% 100%;
    box-shadow: 0 0 3px blue;
    display: flex;
    flex-direction: column;
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .left {
      width: 25%;
      flex: 1;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;

      .left_top {
        display: flex;
        flex: 2;
      }

      .left_center {
        display: flex;
        flex: 3;
      }

      .left_bottom {
        display: flex;
        flex: 2;
      }
    }

    .right {
      width: 25%;
      flex: 1;
      display: flex;
      flex-direction: column;

      .right_top {
        display: flex;
        flex: 2;
      }

      .right_center {
        display: flex;
        flex: 3.7;
      }

      .right_bottom {
        display: flex;
        flex: 2;
      }
    }

    .center {
      width: 50%;
      display: flex;
      flex: 2;
      flex-direction: column;

      .center_top {
        display: flex;
        flex: 1;
      }

      .center_center {
        display: flex;
        flex: 1;
      }

      .center_bottom {
        display: flex;
        flex: 2;
      }
    }
  }
}

</style>