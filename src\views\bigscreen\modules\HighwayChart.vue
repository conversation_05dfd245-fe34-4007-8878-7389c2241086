<template>
    <div class="container">
        <dv-border-box-7 class="container_box">
            <div ref="myEchart" class="chart"></div>
        </dv-border-box-7>
    </div>
</template>
  
<script>
import { getAction } from '@/api/manage'
import moment from 'moment'
export default {
    name: 'HighwayChart',
    components:{
        moment
    },
    data() {
        return {
            chart: null,
            options: {},
            url:{
                chartUrl:"/bigScreen/siteStatistic"
            },
            monthList:[],
            checkNumList:[],
            overNumList:[],
            overRateList:[],

        }
    },
    mounted() {
        this.initOptions()
        this.initCharts()
        this.getData();
    },
    methods: {
        moment,
        getData(){
            getAction(this.url.chartUrl,{year:moment().format("YYYY-MM-DD")}).then((res)=>{
                var list = res.result;
                list.forEach((data)=>{
                    this.monthList.push(data["month"]);
                    this.checkNumList.push(data["checkNum"]);
                    this.overNumList.push(data["overNum"]);
                    this.overRateList.push(data["overRate"]);
                    this.options.xAxis[0].data = this.monthList;
                    this.options.series[0].data = this.checkNumList;
                    this.options.series[1].data = this.overNumList;
                    this.options.series[2].data = this.overRateList;
                    this.chart.setOption(this.options)
                });
            });
            
        },

        initOptions() {
            this.options = {
                title: {
                    text: '超限检测站称重数据统计',
                    top: '10px',
                    left: '10px',
                    textStyle: {
                        fontSize: '15',
                        fontWeight: '600',
                        color: '#fff',
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                },
                legend: {
                    left: 'center',
                    bottom: '0px',
                    data: ['检测数据', '超载数据', '超载率'],
                    textStyle: {
                        color: "#fff"
                    },
                    selectedMode: 'single', // 关键修改：设置为单选模式
                    selected: {  // 新增的selected配置
                '检测数据': false,
                '超载数据': true,
                '超载率': false
            }
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '12%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#fff'
                            },
                            interval:0
                        },
                        axisLine:{
                            show:true
                        }
                    },

                ],
                yAxis: [
                    {
                        type: 'value',
                        splitLine: { show: false },//去除网格线
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#fff'
                            },
                            formatter: (value, index) => {
                                console.log(value,'===>value',index,'===>this is index')
                                if (value) return `${value}`
                                return value
                            },
                        },
                        axisLine:{
                            show:true
                        }
                    },
                    {
                        type: 'value',
                        position: 'right',
                        splitLine: { show: false },
                        axisLabel: {
                            show: true,
                            textStyle: { color: '#fff' },
                            formatter: (value) => `${value}%`
                        },
                        axisLine: { show: true }
                    }
                ],
                series: [
                    {
                        name: '检测数据',
                        type: 'line',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        showSymbol: false,
                        yAxisIndex: 0, // 绑定左侧y轴
                        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },
                    {
                        name: '超载数据',
                        type: 'line',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        showSymbol: true,
                        emphasis: {
                            focus: 'series'
                        },
                        yAxisIndex: 0, // 绑定左侧y轴
                        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },
                    {
                        name: '超载率',
                        type: 'line',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        showSymbol: false,
                        yAxisIndex: 1, // 绑定左侧y轴
                        emphasis: {
                            focus: 'series'
                        },
                        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },
                ]
            };
        },
        initCharts() {
            this.chart = this.$echarts.init(this.$refs.myEchart)
            this.chart.setOption(this.options)
        }
    }

}
</script>
  
<style lang="less">
.container {
    position: relative;

    // background: red;
    .container_box {
        width: 95%;
        height: 95%;
        margin: 0 auto;

        .chart {
            width: 100%;
            height: 100%;
        }
    }

}
</style>
  