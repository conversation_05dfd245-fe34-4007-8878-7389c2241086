<template>
    <a-modal
      title="大件运输车辆登记列表"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      :keyboard="true"
      :footer="null"
      width="1100px"
      @cancel="handleCancel"
    >
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
            <a-col :xl="7" :lg="7" :md="8" :sm="24">
                <a-form-item label="站点名称">
                    <j-search-select-tag  placeholder="请输入站点名称" v-model="queryParam.siteCode" dict="base_site,name,code"  :async="true" />
                </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="车牌号码">
                <a-input placeholder="请输入车牌号码" v-model="queryParam.vehicleNo"></a-input>
              </a-form-item>
        </a-col>
        <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="核查时间">
                 <j-date placeholder="请选择填报时间" @change="changeTimeRange" v-model="date"></j-date>
            </a-form-item>
        </a-col>
        <template v-if="toggleSearchStatus">
        <a-col :xl="6" :lg="7" :md="8" :sm="24">
             <a-form-item label="核查人">
                <a-input placeholder="请输入核查人" v-model="queryParam.recorder"></a-input>
              </a-form-item>
        </a-col>
        </template>
        <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                <a @click="handleToggleSearch" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
                </a>
                </span>
         </a-col>
        </a-row>
        
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <!-- table区域-begin -->
     <div>
          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
            <a style="margin-left: 24px" @click="batchDel"  v-if="selectedRowKeys.length > 0">批量审核</a>
        </div>
        <a-table
            ref="table"
            size="middle"
            :scroll="{x:true}"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange,getCheckboxProps: setDefaultSelect}"
            class="j-table-force-nowrap"
            @change="handleTableChange">
            <template slot="infoSlot" slot-scope="text,record">
                <a-popover title="异常数据对比">
                    <template slot="content">
                        <table border="2" width="300" height="90" align="center" bordercolor="black">
                            <thead> 
                                <tr bgcolor="silver" align="center">                                    
                                    <td>名称</td>    
                                    <td>车辆信息</td>
                                </tr>
                            </thead>
                            <tbody>
                                 <tr align="center">                                    
                                    <td bgcolor="silver">车牌号</td>    
                                    <td>{{ record.vehicleNo }}</td>
                                </tr> 
                                 <tr align="center">                                    
                                    <td bgcolor="silver">车牌颜色</td>    
                                    <td>{{ record.plateColor_dictText }}</td>
                                </tr> 
                                <tr  align="center">                                    
                                    <td bgcolor="silver">轴数</td>    
                                    <td>{{ record.axles }} 轴</td>
                                </tr>
                                <tr align="center">                                    
                                    <td bgcolor="silver">实重</td>    
                                    <td>{{ record.total }}</td>
                                </tr>
                               
                            </tbody>
                           
                        </table>
                    </template>
                   
                   <span style="cursor: pointer;">  {{ text }} </span> 
              
                </a-popover>
            </template>
             <template slot="reasonSlot" slot-scope="text,record">
                <a-popover title="详细原因" >
                    <template slot="content">
                        <p v-if="record.reasonDetail">{{ record.reasonDetail }}</p>
                        <a-empty v-else />
                    </template>
                   <span style="cursor: pointer;">  {{ text }} </span> 
                </a-popover>
            </template>
            <template slot="isCertifiedSlot" slot-scope="text,record">
                <a-popover title="具体信息描述" >
                    <template slot="content">
                        <div v-if="text=='1'">
                            <p>{{ record.uncertifiedType_dictText }}</p>
                            <p>{{ record.detaildesc }}</p>
                        </div>
                        <a-empty v-else />
                    </template>
                   <span style="cursor: pointer;"> {{ tranAbnormalProcess(text) }} </span> 
                </a-popover>
            </template>
            <template slot="fileSlot" slot-scope="text">
            <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
            <a-button
                v-else
                :ghost="true"
                type="primary"
                icon="download"
                size="small"
                @click="downloadFile(text)">
                下载
            </a-button>
            </template>
            <span slot="action" slot-scope="text, record">
                <!-- <a @click="handleDetail(record)" >审核</a> -->
                <a-popconfirm
                        title="你确认通过审核吗？"
                        ok-text="通过"
                        cancel-text="驳回"
                        @confirm="confirm(record)"
                        @cancel="cancel(record)"
                    >
                    <a href="#">审核</a>
                </a-popconfirm>
          
            </span>
      </a-table>
     </div>
    </a-modal>
</template>

<script>
    import '@/assets/less/TableExpand.less'
    import { mixinDevice } from '@/utils/mixin'
    import { JeecgListMixin } from '@/mixins/JeecgListMixin'
    import { putAction } from '@/api/manage'
    import moment from 'moment'
    export default {
        name: 'BigSiteModal',
        mixins:[JeecgListMixin,mixinDevice],
        components: {
        },
        data() {
            return {
                description: '异常修改记录表管理页面',
                visible: false, //显示控制器
                confirmLoading: false,
                filteredInfo: null,
                //日期选择
                dataopen: false, // 默认是否打开弹框,
                userinfo:'',
                date:'',
                props: {
                    disabled:true,
                },
                // 表头
                columns: [
                    {
                        title:'站点名称',
                        align:"center",
                        dataIndex: 'siteCode_dictText'
                    },
                    {
                        title:'车牌号码',
                        align:"center",
                        dataIndex: 'vehicleNo',
                        scopedSlots: {customRender: 'infoSlot'}
                    },
                    {
                        title:'许可证号',
                        align:"center",
                        dataIndex: 'licNo',
                    },
                    {
                        title:'核查人员',
                        align:"center",
                        dataIndex: 'recorder'
                    },
                    {
                        title:'核查时间',
                        align:"center",
                        dataIndex: 'recordTime',
                    },
                    {
                        title:'审核人员',
                        align:"center",
                        dataIndex: 'reviewer'
                    },
                    {
                        title:'核查文件',
                        align:"center",
                        dataIndex: 'fileList',
                        scopedSlots:{customRender: 'fileSlot'},
                    },
                     {
                        title:'是否车证一致',
                        align:"center",
                        dataIndex: 'isCertified',
                        scopedSlots:{customRender: 'isCertifiedSlot'},
                        filters: [
                            {
                                text: '否',
                                value: 0,
                            },
                            {
                                text: '是',
                                value: 1,
                            },
                         
                        ],
                        filterMultiple: false,
                        onFilter: (value, record) => {
                            return record.isCertified == value;
                        }
                    },
                    {
                        title: '操作',
                        dataIndex: 'action',
                        align:"center",
                        width:147,
                        scopedSlots: { customRender: 'action' }
                    }
                ],
                url: {
                    list: "/abnormal/bigIdCars/list",
                },
            }
        },
        methods:{
            //显示model
            showModal() {
                this.visible = true;
                this.loadData();
            },
            handleOk(){
                this.confirmLoading = true;
            },
            handleCancel(e){
                this.visible = false;
            },
          
            handleTableChange(pagination, filters, sorter) {
                //分页、排序、筛选变化时触发
                //TODO 筛选
                this.queryParam.abnormalProcessStatus = filters.abnormalProcessStatus[0];
                if (Object.keys(sorter).length > 0) {
                    this.isorter.column = sorter.field;
                    this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
                }
                this.ipagination = pagination;
                this.loadData();
            },
            //审核
            confirm(record){
                var params = {};
                params.id = record.id;
                params.reviewer = this.$store.getters.userInfo.username;
                putAction(this.url.approval,params).then((res)=>{
                     if(res.success){
                        this.$message.success('审核数据成功!');
                        this.loadData();
                    }else{
                         this.$message.error('审核数据失败!');
                    }
                });
            },
            //驳回
            cancel(record){
                var params = {};
                params.id = record.id;
                params.reviewer = this.$store.getters.userInfo.username;
                putAction(this.url.reject,params).then((res)=>{
                    if(res.success){
                        this.$message.success('驳回数据成功!');
                        this.loadData();
                    }else{
                         this.$message.error('驳回数据失败!');
                    }
                });
            },
            setDefaultSelect(record){
               return ({
                    props: {
                        disabled: record.abnormalProcessStatus == 1 || record.abnormalProcessStatus==3
                     }
                })
            },
            //批量审核
            batchDel: function () {
                if(!this.url.batchApproval){
                    this.$message.error("请设置url.batchApproval属性!")
                    return
                }
                if (this.selectedRowKeys.length <= 0) {
                    this.$message.warning('请选择一条记录！');
                    return;
                } else {
                    var ids = "";
                    for (var a = 0; a < this.selectedRowKeys.length; a++) {
                    ids += this.selectedRowKeys[a] + ",";
                    }
                    var that = this;
                    this.$confirm({
                    title: "确认审核",
                    content: "是否审核选中数据?",
                    onOk: function () {
                        that.loading = true;
                        putAction(that.url.batchApproval, {ids: ids}).then((res) => {
                        if (res.success) {
                            //重新计算分页问题
                            that.reCalculatePage(that.selectedRowKeys.length)
                            that.$message.success(res.message);
                            that.loadData();
                            that.onClearSelected();
                        } else {
                            that.$message.warning(res.message);
                        }
                        }).finally(() => {
                            that.loading = false;
                        });
                    }
                    });
                }
            },
            changeTimeRange(record){
                this.queryParam.recordTime_begin = moment(record).startOf('day').format('YYYY-MM-DD HH:mm:ss');
                this.queryParam.recordTime_end = moment(record).endOf('day').format('YYYY-MM-DD HH:mm:ss');
            },
            //转换审核状态
            tranAbnormalProcess(text){
                switch(text)
                {
                    case 0:
                        return "否";
                    case 1:
                        return "是";
                }
            }
        },
    
    }
 
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>