<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="所属站点">
              <j-dict-select-tag placeholder="请选择所属站点" v-model="queryParam.siteCode" dictCode="base_site,name,code"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('检测设备基础信息表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        <span style="float:right;">
          <a @click="loadData()"><a-icon type="sync" />刷新</a>
          <a-divider type="vertical" />
          <a-popover title="自定义列" trigger="click" placement="leftBottom">
            <template slot="content">
              <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                <a-row style="width: 400px">
                  <template v-for="(item,index) in defColumns">
                    <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span="12"><a-checkbox :value="item.dataIndex"><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox></a-col>
                    </template>
                  </template>
                </a-row>
              </a-checkbox-group>
            </template>
            <a><a-icon type="setting" />设置</a>
          </a-popover>
        </span>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <base-equipment-info-modal ref="modalForm" @ok="modalFormOk"></base-equipment-info-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import Vue from 'vue'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseEquipmentInfoModal from './modules/BaseEquipmentInfoModal.Style#Drawer'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'BaseEquipmentInfoList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      BaseEquipmentInfoModal
    },
    data () {
      return {
        description: '检测设备基础信息表管理页面',
        //表头
        columns:[],
        //列设置
        settingColumns:[],
        // 表头
        defColumns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'车道编号',
            align:"center",
            dataIndex: 'checkLine'
          },
          {
            title:'所属站点',
            align:"center",
            dataIndex: 'siteCode_dictText'
          },
          {
            title:'称重设备类型',
            align:"center",
            dataIndex: 'equipType'
          },
          {
            title:'称重设备检定等级',
            align:"center",
            dataIndex: 'equipLevel'
          },
          {
            title:'称重设备状态',
            align:"center",
            dataIndex: 'equipStatus_dictText'
          },
          {
            title:'车辆分离器',
            align:"center",
            dataIndex: 'hasVehicleSeparator_dictText'
          },
          {
            title:'轮轴识别器',
            align:"center",
            dataIndex: 'hasAxleRecognizer_dictText'
          },
          {
            title:'尺寸检测设备',
            align:"center",
            dataIndex: 'hasMeasuringEquipment_dictText'
          },
          {
            title:'自动栏杆',
            align:"center",
            dataIndex: 'hasAutomicRailing_dictText'
          },
          {
            title:'显示屏',
            align:"center",
            dataIndex: 'hasVeiwingScreen_dictText'
          },
          {
            title:'路侧单元（RSU）',
            align:"center",
            dataIndex: 'hasRsu_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/system/baseEquipmentInfo/list",
          delete: "/system/baseEquipmentInfo/delete",
          deleteBatch: "/system/baseEquipmentInfo/deleteBatch",
          exportXlsUrl: "/system/baseEquipmentInfo/exportXls",
          importExcelUrl: "system/baseEquipmentInfo/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
      this.initColumns();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'checkLine',text:'车道编号',dictCode:''})
        fieldList.push({type:'string',value:'siteCode',text:'所属站点',dictCode:"base_site,name,code"})
        fieldList.push({type:'string',value:'siteId',text:'所属站点(部级编码)',dictCode:''})
        fieldList.push({type:'string',value:'equipType',text:'称重设备类型',dictCode:''})
        fieldList.push({type:'string',value:'equipSize',text:'称重设备尺寸',dictCode:''})
        fieldList.push({type:'string',value:'equipLevel',text:'称重设备检定等级',dictCode:''})
        fieldList.push({type:'string',value:'equipFunc',text:'称重设备实现功能',dictCode:''})
        fieldList.push({type:'int',value:'equipStatus',text:'称重设备状态',dictCode:'is_normal'})
        fieldList.push({type:'string',value:'equipModel',text:'称重设备型号',dictCode:''})
        fieldList.push({type:'string',value:'equipMaker',text:'称重设备生产厂商',dictCode:''})
        fieldList.push({type:'date',value:'instDate',text:'称重设备安装日期'})
        fieldList.push({type:'int',value:'checkZq',text:'称重设备检验周期',dictCode:''})
        fieldList.push({type:'date',value:'checkDate',text:'称重设备检验日期'})
        fieldList.push({type:'string',value:'checkDepartment',text:'称重设备计量检定部门',dictCode:''})
        fieldList.push({type:'string',value:'checkResult',text:'设备检定结果',dictCode:''})
        fieldList.push({type:'string',value:'recognitionCameraMaker',text:'车牌识别摄像机厂家',dictCode:''})
        fieldList.push({type:'string',value:'recognitionCameraModel',text:'车牌识别摄像机型号',dictCode:''})
        fieldList.push({type:'int',value:'hasWeighingControl',text:'是否有称重控制仪表',dictCode:'has_equ'})
        fieldList.push({type:'int',value:'hasVehicleSeparator',text:'是否有车辆分离器',dictCode:'has_equ'})
        fieldList.push({type:'int',value:'hasAxleRecognizer',text:'是否有轮轴识别器',dictCode:'has_equ'})
        fieldList.push({type:'int',value:'hasMeasuringEquipment',text:'是否有尺寸检测设备',dictCode:'has_equ'})
        fieldList.push({type:'int',value:'hasAutomicRailing',text:'是否有自动栏杆',dictCode:'has_equ'})
        fieldList.push({type:'int',value:'hasVeiwingScreen',text:'是否有显示屏',dictCode:'has_equ'})
        fieldList.push({type:'int',value:'hasRsu',text:'是否安装路侧单元（RSU）',dictCode:'has_equ'})
        this.superFieldList = fieldList
      },
      initColumns(){
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');
        var key = this.$route.name+":colsettings";
        let colSettings= Vue.ls.get(key);
        if(colSettings==null||colSettings==undefined){
          let allSettingColumns = [];
          this.defColumns.forEach(function (item,i,array ) {
            allSettingColumns.push(item.dataIndex);
          })
          this.settingColumns = allSettingColumns;
          this.columns = this.defColumns;
        }else{
          this.settingColumns = colSettings;
          const cols = this.defColumns.filter(item => {
            if(item.key =='rowIndex'|| item.dataIndex=='action'){
              return true;
            }
            if (colSettings.includes(item.dataIndex)) {
              return true;
            }
            return false;
          })
          this.columns =  cols;
        }
      },
         onColSettingsChange (checkedValues){
          var key = this.$route.name+":colsettings";
          Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
          this.settingColumns = checkedValues;
          const cols = this.defColumns.filter(item => {
            if(item.key =='rowIndex'|| item.dataIndex=='action'){
              return true
            }
            if (this.settingColumns.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns =  cols;
      },
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>