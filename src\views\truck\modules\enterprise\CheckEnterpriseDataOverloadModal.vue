<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <check-enterprise-data-overload-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></check-enterprise-data-overload-form>
  </j-modal>
</template>

<script>

  import CheckEnterpriseDataOverloadForm from './CheckEnterpriseDataOverloadForm'
  export default {
    name: 'CheckEnterpriseDataOverloadModal',
    components: {
      CheckEnterpriseDataOverloadForm
    },
    data () {
      return {
        title:'',
        width:1024,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>