<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="日期">
              <j-date v-model="queryParam.createTime" placeholder="请选择日期" :disabledDate="disabledToYestoday" :defalutValue="defalutValue"></j-date>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('站点日报')">导出</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
          selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" bordered :columns="columns" :dataSource="dataSource"
        :loading="loading" :pagination="false" class="j-table-force-nowrap" @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
      </a-table>
    </div>

    <statistics-site-data-status-modal ref="modalForm" @ok="modalFormOk"></statistics-site-data-status-modal>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import StatisticsSiteDataStatusModal from './modules/StatisticsSiteDataStatusModal'
import JDate from '../../components/jeecg/JDate.vue'
import moment from 'moment'
export default {
  name: 'StatisticsSiteDataStatusList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    StatisticsSiteDataStatusModal,
    JDate
  },
  data() {
    return {
      description: '站点日报',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function (t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '行政区划',
          align: "center",
          dataIndex: 'distCode_dictText'
        },
        {
          title: '站点名称',
          align: "center",
          dataIndex: 'siteCode_dictText'
        },
        {
          title: '过车数',
          align: "center",
          dataIndex: 'checkNum'
        },
        {
          title: '初检总数',
          align: "center",
          dataIndex: 'firstCheck'
        },
        {
          title: '复检总数',
          align: "center",
          dataIndex: 'recheck'
        },
        {
          title: '动态磅数量',
          align: "center",
          dataIndex: 'dynamicPound'
        },
        {
          title: '静态磅数量',
          align: "center",
          dataIndex: 'staticPound'
        },
        {
          title: '大于一吨',
          align: "center",
          dataIndex: 'overGtOne'
        },
        {
          title: '小于一吨',
          align: "center",
          dataIndex: 'overLtOne'
        },
        {
          title: '超载数',
          align: "center",
          dataIndex: 'overNum'
        },
        {
          title: '上传总数',
          align: "center",
          dataIndex: 'upload'
        },

      ],
      url: {
        list: "/statistics/statisticsSiteDataStatus/list",
        delete: "/statistics/statisticsSiteDataStatus/delete",
        deleteBatch: "/statistics/statisticsSiteDataStatus/deleteBatch",
        exportXlsUrl: "/statistics/statisticsSiteDataStatus/exportXls",
        importExcelUrl: "statistics/statisticsSiteDataStatus/importExcel",

      },
      dictOptions: {},
      superFieldList: [],
      queryParam:{
        createTime : moment().subtract(1, 'days').endOf('day').format("YYYY-MM-DD 00:00:00"),
      },
      defalutValue:  moment().subtract(1, 'days').endOf('day'),
    }
  },
  created() {
    this.queryParam.createTime = moment().subtract(1, 'days').endOf('day').format("YYYY-MM-DD 00:00:00")
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    moment,
    initDictConfig() {
    },
    disabledToYestoday(current) {
      return current && current > moment().subtract(1, 'days').endOf('day')
    },
    searchReset() {
      this.queryParam = {}
      this.queryParam.createTime = moment().subtract(1, 'days').endOf('day').format("YYYY-MM-DD 00:00:00");
      this.loadData(1);
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>