<template>
  <div class="page-header-index-wide">
    <a-row :gutter="24">
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="站点超载数" :total="site.overNum">
          <a-tooltip :title="site.year" slot="action">
            <a-icon type="info-circle-o" />
          </a-tooltip>
          <div>
            <mini-area :dataSource="site.miniData" y="过车数" />
          </div>
          <template slot="footer">
            <div>
              <trend :flag="tranFlag(site.momFlag)" style="margin-right: 16px">
                <span slot="term">月环比</span>
                {{ site.mom }}
              </trend>
              <trend :flag="tranFlag(site.wowFlag)">
                <span slot="term">周环比</span>
                {{ site.wow }}
              </trend>
            </div>
          </template>
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="源头企业超载数" :total="enterprise.overNum">
          <a-tooltip :title="enterprise.year" slot="action">
            <a-icon type="info-circle-o" />
          </a-tooltip>
          <div>
            <mini-area :dataSource="enterprise.miniData" y="过车数" />
          </div>
          <template slot="footer">
            <div>
              <trend :flag="tranFlag(enterprise.momFlag)" style="margin-right: 16px">
                <span slot="term">月环比</span>
                {{ enterprise.mom }}
              </trend>
              <trend :flag="tranFlag(enterprise.wowFlag)">
                <span slot="term">周环比</span>
                {{ enterprise.wow }}
              </trend>
            </div>
          </template>
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="无人站点超载数" :total="unattended.overNum">
          <a-tooltip :title="unattended.year" slot="action">
            <a-icon type="info-circle-o" />
          </a-tooltip>
          <div>
            <mini-area :dataSource="unattended.miniData" y="过车数" />
          </div>
          <template slot="footer">
            <div>
              <trend :flag="tranFlag(unattended.momFlag)" style="margin-right: 16px">
                <span slot="term">月环比</span>
                {{ unattended.mom }}
              </trend>
              <trend :flag="tranFlag(unattended.wowFlag)">
                <span slot="term">周环比</span>
                {{ unattended.wow }}
              </trend>
            </div>
          </template>
        </chart-card>
      </a-col>
      <a-col :sm="24" :md="12" :xl="6" :style="{ marginBottom: '24px' }">
        <chart-card :loading="loading" title="不进站电子抓拍" :total="police.carNum">
          <a-tooltip :title="police.year" slot="action">
            <a-icon type="info-circle-o" />
          </a-tooltip>
          <div>
            <mini-area :dataSource="police.miniData" y="违法车辆" />
          </div>
          <template slot="footer">
            <trend :flag="tranFlag(police.momFlag)" style="margin-right: 16px">
              <span slot="term">月环比</span>
              {{ police.mom }}
            </trend>
            <trend :flag="tranFlag(police.wowFlag)">
              <span slot="term">周环比</span>
              {{ police.wow }}
            </trend>
          </template>
        </chart-card>
      </a-col>
    </a-row>

    <a-card :loading="loading" :bordered="false" :body-style="{ padding: '0' }">
      <div class="salesCard">
        <a-tabs default-active-key="1" size="large" :tab-bar-style="{ marginBottom: '24px', paddingLeft: '16px' }">
          <div class="extra-wrapper" slot="tabBarExtraContent">
            <div class="extra-item">
              <a @click="toDate('day')">今日</a>
              <a @click="toDate('week')">本周</a>
              <a @click="toDate('month')">本月</a>
              <a @click="toDate('year')">本年</a>
            </div>
            <a-range-picker :style="{ width: '256px' }" />
          </div>
          <a-tab-pane loading="true" tab="公路站点" key="1">
            <a-row>
              <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
                <bar title="站点超载趋势" :dataSource="siteBarData" :yaxisText="yAxisText" />
              </a-col>
              <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                <rank-list title="站点超载数排行榜" :list="siteRankList" />
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane tab="源头企业" key="2">
            <a-row>
              <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
                <bar-slider title="站点超载趋势" :dataSource="enterpriseBarData" :originDv="enterpriseBarData" />
              </a-col>
              <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                <rank-list title="企业超载数排行榜" :list="enterpriseRankList" />
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane tab="无人检测站点" key="3">
            <a-row>
              <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
                <bar title="无人站点超载趋势" :dataSource="unattendedBarData" :yaxisText="yAxisText" />
              </a-col>
              <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                <rank-list title="无人站点超载数排行榜" :list="unattendedRankList" />
              </a-col>
            </a-row>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-card>

    <a-row>
      <a-col :span="24">
        <a-card :loading="loading" :bordered="false" title="最近一周超载车辆统计" :style="{ marginTop: '24px' }">
          <a-row>
            <a-col :span="6">
              <head-info title="今日站点过车数" :content="week.siteToday"></head-info>
            </a-col>
            <a-col :span="2">
              <a-spin class="circle-cust">
                <a-icon slot="indicator" type="environment" style="font-size: 24px" />
              </a-spin>
            </a-col>
            <a-col :span="6">
              <head-info title="今日企业过车数" :content="week.enterpriseToday"></head-info>
            </a-col>
            <a-col :span="2">
              <a-spin class="circle-cust">
                <a-icon slot="indicator" type="team" style="font-size: 24px" />
              </a-spin>
            </a-col>
            <a-col :span="6">
              <head-info title="今日无人过车数" :content="week.unattendedToday"></head-info>
            </a-col>
            <a-col :span="2">
              <a-spin class="circle-cust">
                <a-icon slot="indicator" type="rise" style="font-size: 24px" />
              </a-spin>
            </a-col>
          </a-row>
          <line-chart-multid :fields="visitFields" :dataSource="week.weekList"  :aliases="weekAliases"></line-chart-multid>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import ChartCard from '@/components/ChartCard'
import ACol from 'ant-design-vue/es/grid/Col'
import ATooltip from 'ant-design-vue/es/tooltip/Tooltip'
import MiniArea from '@/components/chart/MiniArea'
import MiniBar from '@/components/chart/MiniBar'
import MiniProgress from '@/components/chart/MiniProgress'
import RankList from '@/components/chart/RankList'
import Bar from '@/components/chart/Bar'
import LineChartMultid from '@/components/chart/LineChartMultid'
import HeadInfo from '@/components/tools/HeadInfo.vue'
import moment from 'moment'
import Trend from '@/components/Trend'
import BarSlider from '@/components/chart/BarSlider'
import {
  getLoginfo,
  getVisitInfo,
  showSiteData,
  showSiteBarData,
  showEnterpriseData,
  showEnterpriseBarData,
  showUnattendedData,
  showUnattendedBarData,
  showPoliceData,
  showWeekData,
} from '@/api/api'

const rankList = []
for (let i = 0; i < 7; i++) {
  rankList.push({
    name: '白鹭岛 ' + (i + 1) + ' 号店',
    total: 1234.56 - i * 100,
  })
}
const barData = []
for (let i = 0; i < 12; i += 1) {
  barData.push({
    x: `${i + 1}月`,
    y: Math.floor(Math.random() * 1000) + 200,
  })
}
export default {
  name: 'IndexChart',
  components: {
    ATooltip,
    ACol,
    ChartCard,
    MiniArea,
    MiniBar,
    MiniProgress,
    RankList,
    Bar,
    Trend,
    LineChartMultid,
    HeadInfo,
    BarSlider,
  },
  data() {
    return {
      loading: true,
      center: null,
      rankList,
      barData,
      loginfo: {},
      visitFields: ['site', 'enterprise', 'unattended'],
      visitInfo: [],
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      startTime: moment(new Date()).startOf('year').format('YYYY-MM-DD'),
      endTime: moment(new Date()).endOf('year').format('YYYY-MM-DD'),
      site: {
        year: moment(new Date()).format('YYYY'),
        mom: 0,
        wow: 0,
        momFlag: 1,
        wowFlag: 1,
        overNum: '0',
        miniData: [],
      },
      enterprise: {
        year: moment(new Date()).format('YYYY'),
        mom: 0,
        wow: 0,
        momFlag: 1,
        wowFlag: 1,
        overNum: '0',
        miniData: [],
      },
      unattended: {
        year: moment(new Date()).format('YYYY'),
        mom: 0,
        wow: 0,
        momFlag: 1,
        wowFlag: 1,
        overNum: '0',
        miniData: [],
      },
      police: {
        year: moment(new Date()).format('YYYY'),
        mom: 0,
        monFlag: 1,
        miniData: [],
      },
      siteBarData: [],
      siteRankList: [],
      enterpriseBarData: [],
      enterpriseRankList: [],
      unattendedBarData: [],
      unattendedRankList: [],
      yAxisText: '超载数',
      week:{
        siteToday: "0",
        enterpriseToday: "0",
        unattendedToday: "0",
        weekList:[]
      },
      weekAliases:[
        {field:'site',alias:'站点'},
        {field:'enterprise',alias:'企业'},
        {field:'unattended',alias:'无人站点'},
      ]
    }
  },
  created() {
    setTimeout(() => {
      this.loading = !this.loading
    }, 1000)
    // this.initLogInfo()
    this.initSiteData()
    this.initChartSiteData()
    this.initEnterpriseData()
    this.initChartEnterpriseData()
    this.initUnattendedData()
    this.initChartUnattendedData()
    this.initPoliceData()
    this.initWeekData();
  },
  methods: {
    moment,
    initLogInfo() {
      getLoginfo(null).then((res) => {
        if (res.success) {
          Object.keys(res.result).forEach((key) => {
            res.result[key] = res.result[key] + ''
          })
          this.loginfo = res.result
        }
      })
      getVisitInfo().then((res) => {
        if (res.success) {
          this.week.siteToday = res.result.todaySite;
          this.week.enterpriseToday = res.result.todayEnterprise;
          this.week.unattendedToday = res.result.todayUnattended;
          this.week.weekList = res.result.list
        }
      })
    },
    //获取站点首页card数据
    initSiteData() {
      var params = {
        startTime: this.startTime,
        endTime: this.endTime,
      }
      showSiteData(params).then((res) => {
        if (res.success) {
          this.site.year = res.result.year
          this.site.mom = res.result.mom + '%'
          this.site.momFlag = res.result.momFlag + ''
          this.site.wow = res.result.wow + '%'
          this.site.wowFlag = res.result.wowFlag + ''
          this.site.overNum = res.result.overNum + ''
          this.site.miniData = this.parseMiniArea(res.result.overloadDayList)
        }
      })
    },
    initEnterpriseData() {
      var params = {
        startTime: this.startTime,
        endTime: this.endTime,
      }
      showEnterpriseData(params).then((res) => {
        if (res.success) {
          console.log(res)
          this.enterprise.year = res.result.year
          this.enterprise.mom = res.result.mom + '%'
          this.enterprise.momFlag = res.result.momFlag + ''
          this.enterprise.wow = res.result.wow + '%'
          this.enterprise.wowFlag = res.result.wowFlag + ''
          this.enterprise.overNum = res.result.overNum + ''
          this.enterprise.miniData = this.parseMiniArea(res.result.overloadDayList)
        }
      })
    },
    initUnattendedData() {
      var params = {
        startTime: this.startTime,
        endTime: this.endTime,
      }
      showUnattendedData(params).then((res) => {
        if (res.success) {
          this.unattended.year = res.result.year
          this.unattended.mom = res.result.mom + '%'
          this.unattended.momFlag = res.result.momFlag + ''
          this.unattended.wow = res.result.wow + '%'
          this.unattended.wowFlag = res.result.wowFlag + ''
          this.unattended.overNum = res.result.overNum + ''
          this.unattended.miniData = this.parseMiniArea(res.result.overloadDayList)
        }
      })
    },
    initPoliceData() {
      var params = {
        startTime: this.startTime,
        endTime: this.endTime,
      }
      
      showPoliceData(params).then((res) => {
        if (res.success) {
          this.police.year = res.result.year
          this.police.mom = res.result.mom + '%'
          this.police.momFlag = res.result.momFlag + ''
          this.police.wow = res.result.wow + '%'
          this.police.wowFlag = res.result.wowFlag + ''
          this.police.carNum = res.result.carNum + ''
          this.police.miniData = this.parseMiniArea(res.result.policeDayVoList)
         
        }
      })
    },
    initWeekData() {
      showWeekData().then((res) => {
        if (res.success) {
         this.week.siteToday = res.result.todaySite + "";
          this.week.enterpriseToday = res.result.todayEnterprise + "";
          this.week.unattendedToday = res.result.todayUnattended + "";
          console.log("weekList",res);
          this.week.weekList = res.result.list
        }
      })
    },
    //解析为miniArea 的数据
    parseMiniArea(overloadDayData) {
      if (overloadDayData == null) {
        return overloadDayData
      }
      let data = []
      overloadDayData.forEach((el, index) => {
        data.push({
          x: el.time,
          y: el.num,
        })
      })
      return data
    },
    // 获取站点统计图
    initChartSiteData() {
      var params = {
        startTime: this.startTime,
        endTime: this.endTime,
      }
      showSiteBarData(params).then((res) => {
        if (res.success) {
          console.log(res)
          var data = res.result
          var json = []
          var rankJson = []
          data.forEach(function (element, index) {
            var item = { x: element.sname, y: element.overload }
            var item1 = { name: element.name, total: element.overload }
            json.push(item)
            rankJson.push(item1)
          })
          this.siteBarData = json
          this.siteRankList = this.sortByKey(rankJson, 'total').slice(0, 7)
        }
      })
    },
    // 获取企业统计图
    initChartEnterpriseData() {
      var params = {
        startTime: this.startTime,
        endTime: this.endTime,
      }
      showEnterpriseBarData(params).then((res) => {
        if (res.success) {
          var data = res.result
          var json = []
          var rankJson = []
          data.forEach(function (element, index) {
            var item = { x: element.sname, y: element.overload }
            var item1 = { name: element.name, total: element.overload }
            json.push(item)
            rankJson.push(item1)
          })
          this.enterpriseBarData = json
          this.enterpriseRankList = this.sortByKey(rankJson, 'total').slice(0, 7)
        }
      })
    },
    initChartUnattendedData() {
      var params = {
        startTime: this.startTime,
        endTime: this.endTime,
      }
      showUnattendedBarData(params).then((res) => {
        if (res.success) {
          var data = res.result
          var json = []
          var rankJson = []
          data.forEach(function (element, index) {
            var item = { x: element.sname, y: element.overload }
            var item1 = { name: element.name, total: element.overload }
            json.push(item)
            rankJson.push(item1)
          })
          this.unattendedBarData = json
          this.unattendedRankList = this.sortByKey(rankJson, 'total').slice(0, 7)
        }
      })
    },
    toDate(dateType) {
      switch (dateType) {
        case 'day':
          this.startTime = moment(new Date()).startOf('day').format('YYYY-MM-DD')
          this.endTime = moment(new Date()).endOf('day').format('YYYY-MM-D')
          break
        case 'week':
          this.startTime = moment(new Date()).startOf('week').format('YYYY-MM-DD')
          this.endTime = moment(new Date()).endOf('week').format('YYYY-MM-D')
          break
        case 'month':
          this.startTime = moment(new Date()).startOf('month').format('YYYY-MM-D')
          this.endTime = moment(new Date()).endOf('month').format('YYYY-MM-D')
          break
        case 'year':
          this.startTime = moment(new Date()).startOf('year').format('YYYY-MM-D')
          this.endTime = moment(new Date()).endOf('year').format('YYYY-MM-D')
          break
      }
      this.initSiteData()
      this.initChartSiteData()
      this.initEnterpriseData()
      this.initChartEnterpriseData()
    },

    // 解析flag
    tranFlag(flag) {
      switch (flag) {
        case '1':
          return 'up'
        case '-1':
          return 'down'
        default:
          return ''
      }
    },
    //排序
    sortByKey(array, key) {
      return array.sort(function (a, b) {
        var x = a[key]
        var y = b[key]
        return x > y ? -1 : x < y ? 1 : 0
      })
    },
  },
}
</script>

<style lang="less" scoped>
.circle-cust {
  position: relative;
  top: 28px;
  left: -100%;
}
.extra-wrapper {
  line-height: 55px;
  padding-right: 24px;

  .extra-item {
    display: inline-block;
    margin-right: 24px;

    a {
      margin-left: 24px;
    }
  }
}

/* 首页访问量统计 */
.head-info {
  position: relative;
  text-align: left;
  padding: 0 32px 0 0;
  min-width: 125px;

  &.center {
    text-align: center;
    padding: 0 32px;
  }

  span {
    color: rgba(0, 0, 0, 0.45);
    display: inline-block;
    font-size: 0.95rem;
    line-height: 42px;
    margin-bottom: 4px;
  }
  p {
    line-height: 42px;
    margin: 0;
    a {
      font-weight: 600;
      font-size: 1rem;
    }
  }
}
</style>