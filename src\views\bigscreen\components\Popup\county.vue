<template>
  <div>
    <div class="popWin">
      <div class="popWinBg" v-show="this.show"></div>
      <div class="popWinMain" ref="popWinMain" v-show="this.show">
        <div class="titleBody" ref="titleBody">
          <span>{{ distName }}</span>
        </div>
        <div class="popClose" ref="popClose" v-if="showInner" @click="getHide"></div>
        <div class="slot" v-if="showInner">
          <div class="site_content">
            <!-- <dv-loading>Loading...</dv-loading> -->
            <div class="info_top">
              <a-descriptions
                :title="distName"
                bordered
                class="uniform-col-width"
                :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }"
                size="middle"
              >
                <a-descriptions-item label="今日企业过车数量">
                  {{ detailData.enterpriseCarNum }} 辆
                </a-descriptions-item>
                <a-descriptions-item label="今日企业超载车辆">
                  {{ detailData.enterpriseCarOverNum }} 辆
                </a-descriptions-item>
                <a-descriptions-item label="企业数量">
                  {{ detailData.enterpriseCount }}
                </a-descriptions-item>
                <a-descriptions-item label="企业在线数">
                  {{ detailData.enterpriseOnlineCount }}
                </a-descriptions-item>
              </a-descriptions>
              <a-descriptions
                style="padding-top: 8px"
                bordered
                class="uniform-col-width"
                :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }"
                size="middle"
              >
                <a-descriptions-item label="今日站点过车数量"> {{ detailData.siteCarNum }} 辆 </a-descriptions-item>
                <a-descriptions-item label="今日站点超载数量"> {{ detailData.siteCarOverNum }} 辆 </a-descriptions-item>
                <a-descriptions-item label="站点数量">
                  {{ detailData.siteCount }}
                </a-descriptions-item>
                <a-descriptions-item label="站点在线数">
                  {{ detailData.siteOnlineCount }}
                </a-descriptions-item>
              </a-descriptions>
              <a-descriptions
                style="padding-top: 8px"
                bordered
                :column="{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 2, xs: 2 }"
                size="middle"
                class="uniform-col-width"
              >
                <a-descriptions-item label="今日无人站点过车">
                  {{ detailData.unmannedCarNum }} 辆
                </a-descriptions-item>
                <a-descriptions-item label="今日无人站点超载">
                  {{ detailData.unmannedCarOverNum }} 辆
                </a-descriptions-item>
                <a-descriptions-item label="无人站点">
                  {{ detailData.unmannedCount }}
                </a-descriptions-item>
                <a-descriptions-item label="无人站点在线数">
                  {{ detailData.unmannedOnlineCount }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import gsap from 'gsap'
import SiteLineChart from '@/views/bigscreen/modules/SiteLineChart'
import { getAction } from '@/api/manage'
export default {
  data() {
    return {
      show: false,
      showInner: false,
      url: {},
      info: {
        status: '运行中',
        net_status: '正常',
        name: '',
        data_status: '异常',
        today_check_num: 0,
        today_check_over_num: 0,
      },
      code: '',
      detailData: {},
      distName: '',
    }
  },
  components: { gsap, SiteLineChart },
  props: {
    title: {
      type: String,
      default() {
        return '标题'
      },
    },
  },
  methods: {
    getShow(name) {
      this.distName = name
      console.log(name, '==>name')
      switch (name) {
        case '沁水县':
          this.getData('140521')
          break
        case '阳城县':
          this.getData('140522')
          break
        case '泽州县':
          this.getData('140525')
          break
        case '高平市':
          this.getData('140581')
          break
        case '城区':
          this.getData('140502')
          break
        case '陵川县':
          this.getData('140524')
          break
      }
      this.show = true
      this.showInner = true
    },
    getHide() {
      this.show = false
      this.showInner = false
    },
    getData(code) {
      getAction(`/bigScreen/distInfo`, { distCode: code }).then((res) => {
        this.detailData = res.result
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.popWinBg {
  position: fixed;
  z-index: 8;
  width: 100%;
  background: rgba(0, 0, 0, 0.4);
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: initial;
}

.titleBody {
  background: url(~@/assets/screen/popup/titlebg.png) center center no-repeat;
  width: 100%;
  height: 79px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  flex-direction: row;
  align-content: flex-start;
  top: 4px;
  opacity: 1;
  z-index: 0;

  span {
    font-size: 28px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #ffffff;
    line-height: 32px;
    text-shadow: 0px 2px 3px rgba(17, 20, 22, 0.31);
  }
}

.popWin {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  flex-direction: row;
  align-content: flex-start;
  position: fixed;
  z-index: 10;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  pointer-events: none;
}

.popWinMain {
  pointer-events: initial;
  background: url(~@/assets/screen/popup/popbg.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
  overflow: hidden;
  width: 800px;
  max-width: 1642px;
  height: 50%;
  max-height: 1004px;
  z-index: 100;

  .slot {
    position: absolute;
    top: 80px;
    left: 30px;
    width: calc(100% - 60px);
    height: calc(100% - 100px);
    //background: red;
  }
}

.popClose {
  position: absolute;
  right: 10px;
  top: 10px;
  background: url(~@/assets/screen/popup/icon_close.png) no-repeat;
  width: 64px;
  height: 64px;
  background-size: 100% 100%;
  cursor: pointer;
  z-index: 1;
}
.uniform-col-width {
  .ant-descriptions-item-label .ant-descriptions-item-colon {
    width: 160px;
  }
}
</style>
