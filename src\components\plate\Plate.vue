<template>
    <div class="plate" v-if="ifPlate(plate)">
        <span :class="['plate_left',this.leftColor]">{{ splitPlate(plate)[0] }}</span>
        <span :class="['plate_right',this.rightColor]">{{ splitPlate(plate)[1] }}</span>
    </div>
    <div v-else>
        {{ plate }}
    </div>
</template>
<script>
export default {
    name: "Plate",
    props: {
        plate: {
            type: [String, Function],
            default: "无车牌"
        },
        color: {
            type: [Number, Function],
        }
    },
    created() {
        this.convertColorToCss(this.color)
    },
    data() {
        return {
            leftColor: "",
            rightColor: ""
        }
    },
    methods: {
        //切分车牌
        splitPlate(plate) {
            var plate_one = plate.slice(0,2);
            var plate_two = plate.slice(2);
            return [plate_one,plate_two];
        },
        //转换颜色为样式
        convertColorToCss(color) {
             switch(color){
                case 0:
                    this.leftColor = 'plate_blue';
                    this.rightColor = 'plate_blue'; 
                    break;
                case 1:
                    this.leftColor = 'plate_yellow';
                    this.rightColor = 'plate_yellow';
                     break;
                case 2:
                    this.leftColor = 'plate_black';
                    this.rightColor = 'plate_black'; 
                     break;
                case 3:
                    this.leftColor = 'plate_white';
                    this.rightColor = 'plate_white';
                     break;
                case 4:
                    this.leftColor = 'plate_linerGreen';
                    this.rightColor = 'plate_linerGreen';
                     break;
                case 5:
                    this.leftColor = 'plate_yellow';
                    this.rightColor = 'plate_green';
                     break;
                case 6:
                    this.leftColor = 'plate_liner_blue';
                    this.rightColor = 'plate_liner_blue'; 
                     break;
                case 11:
                    this.leftColor = 'plate_green';
                    this.rightColor = 'plate_green';
                     break;
                case 12:
                    this.leftColor = 'plate_red';
                    this.rightColor = 'plate_red';
                     break;
                default:
                    this.leftColor = 'plate_no';
                    this.rightColor = 'plate_no';
            }
        },
        //是否为车牌号
        ifPlate(plate) {
            if(plate != null) {
                 if(plate.length >= 6) {
                    return true;
                }
            }
            return false;
        }
    }
}
</script>
<style  scoped>
  .plate{
    width: 100%;
    height: 35px;
    border-radius: 5px;
    text-align: center;
    line-height: 35px;
    font-family: 微软雅黑;
    font-weight: bold;
    padding:0 2px;
    box-sizing: border-box;

  }
  .plate_blue{
      border: 1px solid rgb(0, 0, 0);
      background: #3e0eec;
      color: white;
  }
  .plate_yellow{
      border: 1px solid rgb(0, 0, 0);
      background: #e4fd02;
  }
  .plate_black{
      border: 1px solid rgb(0, 0, 0);
      background: #181717;
      color:white;
  }
  .plate_white{
      border: 1px solid rgb(0, 0, 0);
      background: #faf9f9;  
  }
  .plate_linerGreen{
      border: 1px solid rgb(0, 0, 0);
      background: linear-gradient(0, #61ff15, transparent);
  }
  .plate_liner_blue{
      border: 1px solid rgb(0, 0, 0);
      background: linear-gradient(0, #0846ce, transparent);
  }
  .plate_red{
      border: 1px solid rgb(0, 0, 0);
      background: #fc0000;
  }
  .plate_green{
      border: 1px solid rgb(0, 0, 0);
      background: #20d12f;
  }
  .plate_no{
      border: 1px solid rgb(0, 0, 0);
      background: #ccc;
  }
  .plate_left{
    box-sizing: border-box;
    border-right: none;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    display: inline-block;
    width: 40px;
    padding-left: 7px;
    padding-right: 7px;
    position: relative;
  }
  .plate_right{
    box-sizing: border-box;
    border-left: none;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    display: inline-block;
    width: 70px;
  }
  .plate_left:after{
    content: '●';
    position: absolute;
    right: -2px;
    display: inline-block;
    width: 5px;
    
  }
</style>
