<template>
    <div class="container">
        <dv-border-box-8 class="container_box" >
            <div style="box-sizing: border-box;padding: 10px 5px 0px 10px;height: 80%;">
                <div class="header">严重失信行为统计</div>
            <dv-capsule-chart :config="config" style="width:100%;height: 100%;" ref="DvCapsuleChart"/>
            </div>
          
        </dv-border-box-8>
    </div>
</template>
<script>
import { getAction, deleteAction } from '@/api/manage'
export default {
    name: "BlackChart",
    data() {
        return {
            url:{
                    chartUrl:"/bigScreen/blackStatistics"
            },
            config: {
                data: [
                    {
                        name: '车辆',
                        value: 0
                    },
                    {
                        name: '企业',
                        value: 0
                    },
                    {
                        name: '司机',
                        value: 0
                    },
                ],
              
                colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
                unit: '个',
                showValue: true
            }
        }
    },
    created(){
        this.getData();
    },
    methods: {
         getData(){
            getAction(this.url.chartUrl).then((res)=>{
                this.config.data = res.result;
                this.$refs.DvCapsuleChart.calcData()
            });
         }
    }
}
</script>
<style lang="less" scoped>

</style>