<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="案件编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入案件编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <a-input v-model="model.siteCode" placeholder="请输入站点编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteName">
              <a-input v-model="model.siteName" placeholder="请输入站点名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="案件类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <a-input-number v-model="model.type" placeholder="请输入案件类型" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="案件来源" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="form">
              <a-input v-model="model.form" placeholder="请输入案件来源"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="案由" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reason">
              <a-textarea v-model="model.reason" rows="4" placeholder="请输入案由" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法支队名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfTeam">
              <a-input v-model="model.zfTeam" placeholder="请输入执法支队名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车辆通行证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="xcz">
              <a-input v-model="model.xcz" placeholder="请输入车辆通行证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="违法行为种类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfType">
              <a-input v-model="model.zfType" placeholder="请输入违法行为种类"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="受案时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="acceptTime">
              <j-date placeholder="请选择受案时间"  v-model="model.acceptTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="立案依据法律" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reasonLaw">
              <a-input v-model="model.reasonLaw" placeholder="请输入立案依据法律"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="立案法律条款" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reasonLawItem">
              <a-textarea v-model="model.reasonLawItem" rows="4" placeholder="请输入立案法律条款" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="处罚依据法律" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="punishLaw">
              <a-input v-model="model.punishLaw" placeholder="请输入处罚依据法律"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="处罚法律条款" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="punishLawItem">
              <a-textarea v-model="model.punishLawItem" rows="4" placeholder="请输入处罚法律条款" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="处罚决定书编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cfjdsNo">
              <a-input v-model="model.cfjdsNo" placeholder="请输入处罚决定书编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="扣留立案依据法律" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="arrestLaw">
              <a-input v-model="model.arrestLaw" placeholder="请输入扣留立案依据法律"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="扣留立案依据条款" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="arrestLawItem">
              <a-textarea v-model="model.arrestLawItem" rows="4" placeholder="请输入扣留立案依据条款" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="行政处罚时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="punishTime">
              <j-date placeholder="请选择行政处罚时间"  v-model="model.punishTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plate">
              <a-input v-model="model.plate" placeholder="请输入车牌号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="挂车号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateGua">
              <a-input v-model="model.plateGua" placeholder="请输入挂车号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axleType">
              <a-input v-model="model.axleType" placeholder="请输入轴型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
              <a-input v-model="model.axles" placeholder="请输入轴数"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="总重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="total">
              <a-input-number v-model="model.total" placeholder="请输入总重" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="货物" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="goods">
              <a-input v-model="model.goods" placeholder="请输入货物"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="运输证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ysz">
              <a-input v-model="model.ysz" placeholder="请输入运输证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="从业证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cyz">
              <a-input v-model="model.cyz" placeholder="请输入从业证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="运输企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ysDw">
              <a-input v-model="model.ysDw" placeholder="请输入运输企业名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="运输企业经营许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ysDwJyxkz">
              <a-input v-model="model.ysDwJyxkz" placeholder="请输入运输企业经营许可证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="货运源头单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ytDw">
              <a-input v-model="model.ytDw" placeholder="请输入货运源头单位名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="货运源头单位许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ytDwJyxkz">
              <a-input v-model="model.ytDwJyxkz" placeholder="请输入货运源头单位许可证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法人员1编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfPerson1Code">
              <a-input v-model="model.zfPerson1Code" placeholder="请输入执法人员1编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法人员1姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfPerson1Name">
              <a-input v-model="model.zfPerson1Name" placeholder="请输入执法人员1姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法人员1单位及职务" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfPerson1Dwzw">
              <a-input v-model="model.zfPerson1Dwzw" placeholder="请输入执法人员1单位及职务"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法人员2编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfPerson2Code">
              <a-input v-model="model.zfPerson2Code" placeholder="请输入执法人员2编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法人员2姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfPerson2Name">
              <a-input v-model="model.zfPerson2Name" placeholder="请输入执法人员2姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法人员2单位及职务" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zfPerson2Dwzw">
              <a-input v-model="model.zfPerson2Dwzw" placeholder="请输入执法人员2单位及职务"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否结案" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isFinish">
              <j-dict-select-tag type="list" v-model="model.isFinish" dictCode="" placeholder="请选择是否结案" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="结案日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endDate">
              <j-date placeholder="请选择结案日期"  v-model="model.endDate" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverName">
              <a-input v-model="model.driverName" placeholder="请输入司机姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverSex">
              <j-dict-select-tag type="list" v-model="model.driverSex" dictCode="" placeholder="请选择司机性别" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机年龄" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverAge">
              <a-input-number v-model="model.driverAge" placeholder="请输入司机年龄" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverIdCard">
              <a-input v-model="model.driverIdCard" placeholder="请输入司机身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机单位及职务" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverDwzw">
              <a-input v-model="model.driverDwzw" placeholder="请输入司机单位及职务"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机住址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverAddress">
              <a-input v-model="model.driverAddress" placeholder="请输入司机住址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dirverTel">
              <a-input v-model="model.dirverTel" placeholder="请输入司机电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机驾驶证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverJszh">
              <a-input v-model="model.driverJszh" placeholder="请输入司机驾驶证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="司机邮编" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverPosetCode">
              <a-input v-model="model.driverPosetCode" placeholder="请输入司机邮编"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="当事人职业" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dsrZy">
              <a-input v-model="model.dsrZy" placeholder="请输入当事人职业"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="当事人企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dsrQyName">
              <a-input v-model="model.dsrQyName" placeholder="请输入当事人企业名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="当事人企业法人代表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dsrQyLegalPerson">
              <a-input v-model="model.dsrQyLegalPerson" placeholder="请输入当事人企业法人代表"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="当事人企业地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dsrQyAddress">
              <a-input v-model="model.dsrQyAddress" placeholder="请输入当事人企业地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="当事人企业电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dsrQyTel">
              <a-input v-model="model.dsrQyTel" placeholder="请输入当事人企业电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czQyName">
              <a-input v-model="model.czQyName" placeholder="请输入车主企业名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主企业法人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czQyLegalPerson">
              <a-input v-model="model.czQyLegalPerson" placeholder="请输入车主企业法人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主企业地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czQyAddress">
              <a-input v-model="model.czQyAddress" placeholder="请输入车主企业地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主企业电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czQyTel">
              <a-input v-model="model.czQyTel" placeholder="请输入车主企业电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czName">
              <a-input v-model="model.czName" placeholder="请输入车主姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czSex">
              <j-dict-select-tag type="list" v-model="model.czSex" dictCode="" placeholder="请选择车主性别" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主身份证" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czIdCard">
              <a-input v-model="model.czIdCard" placeholder="请输入车主身份证"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主住址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czAddress">
              <a-input v-model="model.czAddress" placeholder="请输入车主住址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车主电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="czTel">
              <a-input v-model="model.czTel" placeholder="请输入车主电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="数据编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dataId">
              <a-input v-model="model.dataId" placeholder="请输入数据编码"  ></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'CaseListForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/cases/caseList/add",
          edit: "/cases/caseList/edit",
          queryById: "/cases/caseList/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>