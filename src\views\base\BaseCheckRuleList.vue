<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-form-item label="车型名称">
              <j-dict-select-tag placeholder="请选择车型名称" v-model="queryParam.carTypeName" dictCode="car_type_name"/>
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="轴数">
              <j-dict-select-tag placeholder="请选择轴数" v-model="queryParam.axles" dictCode="axles"/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="轴型">
                <a-input placeholder="请输入轴型" v-model="queryParam.vehicleAxlesType"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="是否默认">
                <j-dict-select-tag placeholder="请选择是否默认" v-model="queryParam.isDefault" dictCode="is_default"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('超限超载规则表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <base-check-rule-modal ref="modalForm" @ok="modalFormOk"></base-check-rule-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseCheckRuleModal from './modules/BaseCheckRuleModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'BaseCheckRuleList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      BaseCheckRuleModal
    },
    data () {
      return {
        description: '超限超载规则表管理页面',
         isorter:{
          column: 'axles',  //修改默认排序
          order: 'asc',
        },
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'车型名称',
            align:"center",
            dataIndex: 'carTypeName_dictText'
          },
          {
            title:'轴数',
            align:"center",
            sorter: true,
            dataIndex: 'axles_dictText'
          },
          {
            title:'轴型',
            align:"center",
            dataIndex: 'vehicleAxlesType'
          },
          {
            title:'限重',
            align:"center",
            sorter: true,
            dataIndex: 'weightLimit'
          },
          {
            title:'轴型(新)',
            align:"center",
            dataIndex: 'vehicleAxlesTypeDec'
          },
          {
            title:'是否默认',
            align:"center",
            dataIndex: 'isDefault_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/system/baseCheckRule/list",
          delete: "/system/baseCheckRule/delete",
          deleteBatch: "/system/baseCheckRule/deleteBatch",
          exportXlsUrl: "/system/baseCheckRule/exportXls",
          importExcelUrl: "system/baseCheckRule/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'int',value:'carTypeName',text:'车型名称',dictCode:'car_type_name'})
        fieldList.push({type:'int',value:'axles',text:'轴数',dictCode:'axles'})
        fieldList.push({type:'string',value:'vehicleAxlesType',text:'轴型',dictCode:''})
        fieldList.push({type:'int',value:'weightLimit',text:'限重',dictCode:''})
        fieldList.push({type:'int',value:'heightLimit',text:'限高',dictCode:''})
        fieldList.push({type:'int',value:'lengthLimit',text:'限长',dictCode:''})
        fieldList.push({type:'int',value:'widthLimit',text:'限宽',dictCode:''})
        fieldList.push({type:'string',value:'vehicleAxlesTypeDec',text:'轴型(新)',dictCode:''})
        fieldList.push({type:'int',value:'isDefault',text:'是否默认',dictCode:'is_default'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>