<template>
    <a-range-picker :placeholder="['开始月份', '结束月份']" :size="size" :format="format" v-model="monthVal" :mode="mode"
        @change="handleChange"  @panelChange="handlePanelChange"/>
</template>
<script>
import moment from 'moment'

export default {
    name: "JMonthRange",
    components: {
        moment
    },
    props: {
        size: {
            type: String,
            default: 'default',
            required: false
        },
        format: {
            type: String,
            default: 'YYYY-MM',
            required: false
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false
        },
        value: {
            type: Array,
            required: false
        },
    },
    watch: {
        value(val) {
            if (val.length == 0) {
                this.monthVal = null
            } else {
                this.monthVal = val
            }
        }
    },
    created() {
        // console.log("时间", this.dateVal);
    },
    data() {
        let dateStr = this.value;
        return {
            monthVal: dateStr == [] ? null : dateStr,
            mode: ['month', 'month'],
        }

    },

    methods: {
        moment,
        handleChange(dateStr) {
            console.log("aa",dateStr);
            this.$emit('change', dateStr);
        },
        handlePanelChange(value, mode) {
            console.log(value,mode);
            this.monthVal = value;
            this.mode = [mode[0] === 'date' ? 'month' : mode[0], mode[1] === 'date' ? 'month' : mode[1]];
            this.$emit('change', value);
        },

    }
}
</script>
 
<style scoped>
</style>