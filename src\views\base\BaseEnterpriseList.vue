<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业编码">
              <j-input placeholder="请输入企业编码" v-model="queryParam.code"></j-input>
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业名称">
              <j-input placeholder="请输入企业名称" v-model="queryParam.enterpriseName"></j-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="5" :lg="7" :md="8" :sm="24">
              <a-form-item label="行政编码">
                <j-area-linkage type="cascader" v-model="queryParam.distCode" placeholder="请选择省市区"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('货运源头企业表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
           <span style="float:right;">
          <a @click="loadData()"><a-icon type="sync" />刷新</a>
          <a-divider type="vertical" />
          <a-popover title="自定义列" trigger="click" placement="leftBottom">
            <template slot="content">
              <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                <a-row style="width: 400px">
                  <template v-for="(item,index) in defColumns">
                    <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span="12"><a-checkbox :value="item.dataIndex"><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox></a-col>
                    </template>
                  </template>
                </a-row>
              </a-checkbox-group>
            </template>
            <a><a-icon type="setting" />设置</a>
          </a-popover>
        </span>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="pcaSlot" slot-scope="text">
          <div>{{ getPcaText(text) }}</div>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <base-enterprise-modal ref="modalForm" @ok="modalFormOk"></base-enterprise-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import Vue from 'vue'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseEnterpriseModal from './modules/BaseEnterpriseModal.Style#Drawer'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import Area from '@/components/_util/Area'

  export default {
    name: 'BaseEnterpriseList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      BaseEnterpriseModal
    },
    data () {
      return {
        description: '货运源头企业表管理页面',
         // 表头
        columns:[],
        //列设置
        settingColumns:[],
        isorter:{
          column: 'create_time',  //修改默认排序
          order: 'asc',
        },
        //列定义
        defColumns:[
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'企业名称',
            align:"center",
            dataIndex: 'enterpriseName'
          },
           {
            title:'企业简称',
            align:"center",
            dataIndex: 'shortName'
          },
          {
            title:'行政编码',
            align:"center",
            dataIndex: 'distCode',
            scopedSlots: {customRender: 'pcaSlot'}
          },
          {
            title:'所属管理机构',
            align:"center",
            dataIndex: 'chargeDepartment'
          },
          {
            title:'治超管理部门',
            align:"center",
            dataIndex: 'orgId'
          },
          {
            title:'是否安装称重设备',
            align:"center",
            dataIndex: 'hasWeighing_dictText'
          },
          {
            title:'是否安装车牌抓拍设备',
            align:"center",
            dataIndex: 'hasCamera_dictText'
          },
          {
            title:'是否联网',
            align:"center",
            dataIndex: 'hasNetwork_dictText'
          },
          {
            title:'是否采用电子运单',
            align:"center",
            dataIndex: 'hasYundan_dictText'
          },
          {
            title:'数据状态',
            align:"center",
            dataIndex: 'dataStatus_dictText'
          },
          
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/system/baseEnterprise/list",
          delete: "/system/baseEnterprise/delete",
          deleteBatch: "/system/baseEnterprise/deleteBatch",
          exportXlsUrl: "/system/baseEnterprise/exportXls",
          importExcelUrl: "system/baseEnterprise/importExcel",
          
        },
        dictOptions:{},
        pcaData:'',
        superFieldList:[],
      }
    },
    created() {
      this.pcaData = new Area()
      this.getSuperFieldList();
      this.initColumns();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      getPcaText(code){
        return this.pcaData.getText(code);
      },
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'code',text:'企业编码',dictCode:''})
        fieldList.push({type:'string',value:'enterpriseName',text:'企业名称',dictCode:''})
        fieldList.push({type:'string',value:'sourceCode',text:'信用编码',dictCode:''})
        fieldList.push({type:'pca',value:'distCode',text:'行政编码'})
        fieldList.push({type:'string',value:'enterpriseType',text:'企业类型',dictCode:''})
        fieldList.push({type:'string',value:'chargeDepartment',text:'所属管理机构',dictCode:''})
        fieldList.push({type:'string',value:'orgId',text:'治超管理部门',dictCode:''})
        fieldList.push({type:'string',value:'address',text:'单位地址',dictCode:''})
        fieldList.push({type:'string',value:'postCode',text:'邮政编码',dictCode:''})
        fieldList.push({type:'string',value:'personInCharge',text:'负责人',dictCode:''})
        fieldList.push({type:'string',value:'contactor',text:'联系人',dictCode:''})
        fieldList.push({type:'string',value:'telephone',text:'联系电话',dictCode:''})
        fieldList.push({type:'string',value:'chuanZhen',text:'传真电话',dictCode:''})
        fieldList.push({type:'int',value:'hasWeighing',text:'是否安装称重设备',dictCode:'has_equ'})
        fieldList.push({type:'int',value:'hasCamera',text:'是否安装车牌抓拍设备',dictCode:'has_equ'})
        fieldList.push({type:'string',value:'hasNetwork',text:'是否联网',dictCode:'has_equ'})
        fieldList.push({type:'string',value:'hasYundan',text:'是否采用电子运单',dictCode:'has_yundan'})
        fieldList.push({type:'string',value:'hl',text:'主要出/入厂货物类型',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'longitude',text:'经度',dictCode:''})
        fieldList.push({type:'BigDecimal',value:'latitude',text:'纬度',dictCode:''})
        fieldList.push({type:'string',value:'overloadPlatformCode',text:'市级本平台编码',dictCode:''})
        fieldList.push({type:'string',value:'ministerialOerloadCode',text:'部级系统编码',dictCode:''})
        fieldList.push({type:'int',value:'isFocus',text:'列为重点企业',dictCode:'is_focus'})
        fieldList.push({type:'date',value:'focusDate',text:'中列为重点企业时'})
        this.superFieldList = fieldList
      },
      onColSettingsChange (checkedValues){
          var key = this.$route.name+":colsettings";
          Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
          this.settingColumns = checkedValues;
          const cols = this.defColumns.filter(item => {
            if(item.key =='rowIndex'|| item.dataIndex=='action'){
              return true
            }
            if (this.settingColumns.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns =  cols;
      },
      initColumns(){
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');
        var key = this.$route.name+":colsettings";
        let colSettings= Vue.ls.get(key);
        if(colSettings==null||colSettings==undefined){
          let allSettingColumns = [];
          this.defColumns.forEach(function (item,i,array ) {
            allSettingColumns.push(item.dataIndex);
          })
          this.settingColumns = allSettingColumns;
          this.columns = this.defColumns;
        }else{
          this.settingColumns = colSettings;
          const cols = this.defColumns.filter(item => {
            if(item.key =='rowIndex'|| item.dataIndex=='action'){
              return true;
            }
            if (colSettings.includes(item.dataIndex)) {
              return true;
            }
            return false;
          })
          this.columns =  cols;
        }
      },
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>