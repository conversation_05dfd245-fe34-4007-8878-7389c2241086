<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="车型名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="carTypeName">
              <j-dict-select-tag type="list" v-model="model.carTypeName" dictCode="car_type_name" placeholder="请选择车型名称" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
              <j-dict-select-tag type="list" v-model="model.axles" dictCode="axles" placeholder="请选择轴数" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="轴型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleAxlesType">
              <a-input v-model="model.vehicleAxlesType" placeholder="请输入轴型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="限重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weightLimit">
              <a-input-number v-model="model.weightLimit" placeholder="请输入限重" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="限高" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="heightLimit">
              <a-input-number v-model="model.heightLimit" placeholder="请输入限高" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="限长" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lengthLimit">
              <a-input-number v-model="model.lengthLimit" placeholder="请输入限长" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="限宽" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="widthLimit">
              <a-input-number v-model="model.widthLimit" placeholder="请输入限宽" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="轴型(新)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleAxlesTypeDec">
              <a-input v-model="model.vehicleAxlesTypeDec" placeholder="请输入轴型(新)"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否默认" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isDefault">
              <j-dict-select-tag type="list" v-model="model.isDefault" dictCode="is_default" placeholder="请选择是否默认" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseCheckRuleForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           carTypeName: [
              { required: true, message: '请输入车型名称!'},
           ],
           axles: [
              { required: true, message: '请输入轴数!'},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           vehicleAxlesType: [
              { required: true, message: '请输入轴型!'},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           weightLimit: [
              { required: true, message: '请输入限重!'},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           isDefault: [
              { required: true, message: '请输入是否默认!'},
           ],
        },
        url: {
          add: "/system/baseCheckRule/add",
          edit: "/system/baseCheckRule/edit",
          queryById: "/system/baseCheckRule/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>