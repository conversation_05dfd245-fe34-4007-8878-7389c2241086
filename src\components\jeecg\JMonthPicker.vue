<template>
    <a-date-picker
        dropdownClassName="j-date-picker"
        mode="month"
        format="YYYY-MM"
        :placeholder="placeholder"
        @change="handleDateChange"
        v-model="monthVal"
        :open="dateopen"
        @openChange="openChangeYear"
        @panelChange="panelChangeYear"
        :disabled="disabled"
        :getCalendarContainer="getCalendarContainer"
        v-bind="$attrs"/>
</template>
<script>
import moment from 'moment'
 
export default {
    name: "JMonthPicker",
    components:{
        moment
    },
    props: {
        placeholder: {
            type: String,
            default: '',
            required: false
        },
        value: {
            type: String,
            required: false
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false
        },
        getCalendarContainer: {
            type: Function,
            default: (node) => node.parentNode
        }
    },
    data() {
        let dateStr = this.value;
        return {
            monthVal: !dateStr?null:moment(dateStr).format('YYYY-MM'),
            dateopen: false
        }
    },
    watch: {
        value (val) {
            if(!val){
                this.monthVal = null;
            }else{
                this.monthVal = moment(val).format('YYYY-MM');
            }
        }
    },
    methods: {
        moment,
        handleDateChange(mom, dateStr) {
            this.$emit('change', dateStr);
            this.monthVal=null;
        },
        openChangeYear(status) {
            if (status) {
                this.dateopen = true;
            } else {
                this.dateopen = false;
            }
        },
        // 选择年之后 关闭弹框
        panelChangeYear(value) {
            this.monthVal = value;
            this.dateopen = false;
            this.$emit('input', moment(value).format("YYYY-MM"));
        }
    }
}
</script>
 
<style scoped>
 
</style>