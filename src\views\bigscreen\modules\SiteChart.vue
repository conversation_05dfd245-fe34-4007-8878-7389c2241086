<template>
    <div class="container">
        <dv-border-box-8 class="container_box" style="box-sizing: border-box;padding: 10px 0px 20px 10px">
            <div class="header">超限检测站超载排名</div>
            <dv-capsule-chart :config="config"  ref="DvCapsuleChart" />
        </dv-border-box-8>
    </div>
</template>
<script>
import { getAction, deleteAction } from '@/api/manage'
import moment from 'moment'
export default {
    name: "SiteChart",
    components:{
        moment
    },
    data() {
        return {
            config: {
                data: [
                    {
                        name: '狸猫泉',
                        value: 0
                    },
                    {
                        name: '凤凰山',
                        value: 0
                    },
                    {
                        name: '冶底',
                        value: 0
                    },
                    {
                        name: '宫岭',
                        value: 0
                    },
                    {
                        name: '尉迟',
                        value: 0
                    },
                    {
                        name: '王寨',
                        value: 0
                    },
                    {
                        name: '端氏',
                        value: 0
                    },
                    {
                        name: '上孔',
                        value: 0
                    },
                    {
                        name: '狼儿掌',
                        value: 0
                    },
                    {
                        name: '夺火',
                        value: 0
                    },
                    {
                        name: '营礼',
                        value: 0
                    },
                    {
                        name: '阁河',
                        value: 0
                    },
                    {
                        name: '横水',
                        value: 0
                    },
                    {
                        name: '西岭头',
                        value: 0
                    },
                    {
                        name: '寺庄',
                        value: 0
                    },
                    {
                        name: '换马桥',
                        value: 0
                    },
                    {
                        name: '江河',
                        value: 0
                    }
                  
                ],
                colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
                unit: '辆',
                showValue: true
            },
            url:{
                chartUrl:"/bigScreen/siteOverRank"
            }
        }
    },
    created(){
        this.getData()
    },
    methods: {
        moment,
        getData(){
            getAction(this.url.chartUrl,{year:moment().format("YYYY-MM-DD")}).then((res)=>{
        // 添加排序逻辑（按value降序排列）
        const sortedData = res.result.sort((a, b) => b.value - a.value)
        this.config.data = sortedData;
        this.$refs.DvCapsuleChart.calcData();
    });
            // getAction(this.url.chartUrl,{year:moment().format("YYYY-MM-DD")}).then((res)=>{
            //    this.config.data = res.result;
            //    this.$refs.DvCapsuleChart.calcData();
            // });
        }
    }
}
</script>
<style lang="less" scoped>
.container .container_box {
    width: 95%;
    height: 98%;
    margin: 0 auto;
}

</style>