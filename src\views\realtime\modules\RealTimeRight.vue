<template>
    <a-card class="j-address-list-right-card-box" :loading="cardLoading" :bordered="false">
        <a-row type="flex" :gutter="16">
            <a-col :md="14" :sm="24">
                <div id="divPlugin" class="plugin"></div>
                <div>
                    <a-button>车道号</a-button>
                    <a-button>全屏</a-button>
                    <a-button>窗口分割</a-button>
                    <a-button>回放</a-button>
                </div>
            </a-col>
            <a-col :md="10" :sm="24">
                <a-card :bordered="false">
                    <template v-for="(data, index) in dataSource">
                        <div style="float: left;width:150px;height:150px;margin-right: 10px;margin: 0 8px 8px 0;"
                            :key="index">
                            <div
                                style="width: 100%;height: 100%;position: relative;padding: 8px;border: 1px solid #d9d9d9;border-radius: 4px;">
                                <img style="width: 100%;" :src="data.filePath" preview="index">
                            </div>
                        </div>
                    </template>
                    <a-divider style="margin-bottom: 32px" />
                    <detail-list title="实时数据" :col="this.col" layout="vertical">
                      <detail-list-item term="车牌号">
                            <plate plate="晋E80135"></plate>
                        </detail-list-item>
                        <detail-list-item term="站点名称">狸猫泉超限检测站</detail-list-item>
                        <detail-list-item term="站点状态">运行中</detail-list-item>
                        <detail-list-item term="磅型">动态磅</detail-list-item>
                        <detail-list-item term="检测类型">复检</detail-list-item>
                      
                        <detail-list-item term="轴型">无</detail-list-item>
                        <detail-list-item term="轴数">6轴</detail-list-item>
                        <detail-list-item term="限重">18吨</detail-list-item>
                        <detail-list-item term="是否超载">超载</detail-list-item>
                        <detail-list-item term="超载数">2吨</detail-list-item>
                        <detail-list-item term="超载率">15%</detail-list-item>
                    </detail-list>
                </a-card>
            </a-col>
        </a-row>


    </a-card>
</template>
<script>
import PageLayout from '@/components/page/PageLayout'
import DetailList from '@/components/tools/DetailList'
import Plate from '@/components/plate/Plate'
const DetailListItem = DetailList.Item
export default {
    name: "RealTimeRight",
    props: ['value'],
    components: {
        PageLayout,
        DetailList,
        DetailListItem,
        Plate
    },
    data() {
        return {
            cardLoading: false,
            iWidth: 760,
            iHeight: 550,
            col: 3,
            dataSource: [
                { id: '000', sort: 0, filePath: 'https://static.jeecg.com/upload/test/1_1588149743473.jpg' },
                { id: '111', sort: 1, filePath: 'https://static.jeecg.com/upload/test/u27356337152749454924fm27gp0_1588149731821.jpg' },
                { id: '222', sort: 2, filePath: 'https://static.jeecg.com/upload/test/u24454681402491956848fm27gp0_1588149712663.jpg' },

            ],
        }
    },
    mounted() {
        this.videoInitPlugin();
    },
    methods: {
        videoInitPlugin() {
            var iRet = WebVideoCtrl.I_CheckPluginInstall();
            if (iRet === -1) {
                alert("您还未安装过插件，请安装WebComponentsKit.exe插件！");
                return;
            }
            this.initPlugin();

        },
        initPlugin() {
            var that = this;
            WebVideoCtrl.I_InitPlugin(this.iWidth, this.iHeight, {
                bWndFull: true,     //是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
                iPackageType: 2,    //2:PS 11:MP4
                iWndowType: 1,
                bNoPlugin: false,
                cbSelWnd: function (xmlDoc) {
                    console.log(xmlDoc);
                },
                cbInitPluginComplete: function () {
                    WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin");
                    // 检查插件是否最新
                    if (-1 == WebVideoCtrl.I_CheckPluginVersion()) {
                        alert("检测到新的插件版本，双击开发包目录里的WebComponentsKit.exe升级！");
                        return;
                    }
                }
            });
            console.log("加载视频完成");
            WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin");
        }
    }
}
</script>
<style>
.j-address-list-right-card-box .ant-table-placeholder {
    min-height: 46px;
}

.plugin {
    width: 760px;
    height: 600px;
}
</style>
<style scoped>
.j-address-list-right-card-box {
    height: 100%;
    min-height: 300px;
}

/deep/ .detail-list .content {
    color: rgba(0, 0, 0, 0.65);
    display: table-cell;
    line-height: 22px;
    width:auto
}
/deep/ .detail-list .ant-col {
    height:85px;
}
</style>