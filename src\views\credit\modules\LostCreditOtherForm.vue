<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="上报年份" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="year">
              <a-input v-model="model.year" placeholder="请输入上报年份"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="上报周期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="season">
              <j-dict-select-tag type="list" v-model="model.season" dictCode="season" placeholder="请选择上报周期" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="失信行为类别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="creditCategory">
              <j-dict-select-tag type="list" v-model="model.creditCategory" dictCode="credit_category" placeholder="请选择失信行为类别" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="当事人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverName">
              <a-input v-model="model.driverName" placeholder="请输入当事人姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverIdCard">
              <a-input v-model="model.driverIdCard" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="companyName">
              <a-input v-model="model.companyName" placeholder="请输入企业名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="统一社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unifiedCreditCode">
              <a-input v-model="model.unifiedCreditCode" placeholder="请输入统一社会信用代码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="法定代表人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="legalRepresentative">
              <a-input v-model="model.legalRepresentative" placeholder="请输入法定代表人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idCardNumber">
              <a-input v-model="model.idCardNumber" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="发布期开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="releaseStartDate">
              <j-date placeholder="请选择发布期开始日期" v-model="model.releaseStartDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="发布期结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="releaseEndDate">
              <j-date placeholder="请选择发布期结束日期" v-model="model.releaseEndDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="公示地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="publishAddress">
              <a-input v-model="model.publishAddress" placeholder="请输入公示地址"  ></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'LostCreditOtherForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           year: [
              { required: true, message: '请输入上报年份!'},
           ],
           season: [
              { required: true, message: '请输入上报周期!'},
           ],
           creditCategory: [
              { required: true, message: '请输入失信行为类别!'},
           ],
           driverName: [
              { required: true, message: '请输入当事人姓名!'},
           ],
           driverIdCard: [
              { required: true, message: '请输入身份证号!'},
           ],
           companyName: [
              { required: true, message: '请输入企业名称!'},
           ],
           legalRepresentative: [
              { required: true, message: '请输入法定代表人!'},
           ],
           idCardNumber: [
              { required: true, message: '请输入身份证号!'},
           ],
           releaseStartDate: [
              { required: true, message: '请输入发布期开始日期!'},
           ],
           releaseEndDate: [
              { required: true, message: '请输入发布期结束日期!'},
           ],
        },
        url: {
          add: "/credit/lostCreditOther/add",
          edit: "/credit/lostCreditOther/edit",
          queryById: "/credit/lostCreditOther/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>