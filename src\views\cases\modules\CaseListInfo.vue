<template>
<div :model="model" :v-if="visible">
  <page-layout :title="`案件编码:${model.code}`" >

    <detail-list slot="headerContent" size="small" :col="1" class="detail-layout">
      <detail-list-item term="站点名称">{{ model.siteName }}</detail-list-item>
      <detail-list-item term="案件类型">{{ model.type_dictText }}</detail-list-item>
      <detail-list-item term="受案时间">{{ model.acceptTime}}</detail-list-item>
      <detail-list-item term="案件来源">{{ model.form }}</detail-list-item>
      <detail-list-item term="执法支队名称">{{ model.zfTezfTeamamName }}</detail-list-item>
      <detail-list-item term="案由">{{ model.reason }}</detail-list-item>
    </detail-list>
    
    <a-row slot="extra" class="status-list">
      <a-col :xs="12" :sm="12">
        <div class="text">状态</div>
        <div class="heading">
          <span v-if="model.isFinish == 0 ">未结案</span>
          <span v-else>
            已结案
          </span>
        </div>
      </a-col>
      <a-col :xs="12" :sm="12" >
        <div v-if="model.isFinish == 1">
           <div class="text">结案时间</div>
           <div class="heading">{{ model.endDate }}</div>
        </div>
      </a-col>
    </a-row>
    <!-- actions -->
    <!-- <template slot="action">
      <a-button type="primary" >上传省平台</a-button>
    </template> -->

    <a-card style="margin-top: 24px" :bordered="false" title="详细信息">
      <detail-list title="车辆信息">
        <a-row>
          <detail-list-item term="车牌号">{{ model.plate }}</detail-list-item>
          <detail-list-item term="挂车号">{{ model.plateGua }}</detail-list-item>
          <detail-list-item term="行车证号">{{ model.xcz }}</detail-list-item>
        </a-row>
        <a-row>
          <detail-list-item term="车型">{{ model.vehicleType }}</detail-list-item>
          <detail-list-item term="轴数">{{ model.axisCount }}</detail-list-item>
          <detail-list-item term="车货总重">{{ model.weightT }}</detail-list-item>
        </a-row>
        <a-row>
          <detail-list-item term="货物种类">{{ model.goods }}</detail-list-item>
          <detail-list-item term="运输证号">{{ model.ysz }}</detail-list-item>
        </a-row>
       
        
      </detail-list>
         <a-divider style="margin: 16px 0" />
        <detail-list title="运输企业" >
          <detail-list-item term="企业名称">{{ model.ysDw }}</detail-list-item>
          <detail-list-item term="企业经营许可证号">{{ model.ysDwJyxkz }}</detail-list-item>
        </detail-list>
        <a-divider style="margin: 16px 0" />
        <detail-list title="货运源头单位">
          <detail-list-item term="单位名称">{{ model.ytDw }}</detail-list-item>
          <detail-list-item term="经营许可证号">{{ model.ytDwJyxkz }}</detail-list-item>
        </detail-list>
           <a-divider style="margin: 16px 0" />
      <detail-list title="执法人员信息">
        <div>
          <detail-list-item term="编号">{{ model.zfPerson1Code }}</detail-list-item>
          <detail-list-item term="姓名">{{ model.zfPerson1Name }}</detail-list-item>
          <detail-list-item term="职务">{{ model.zfPerson1Dwzw }}</detail-list-item>
        </div>
        <div>
          <detail-list-item term="编号">{{ model.zfPerson2Code }}</detail-list-item>
          <detail-list-item term="姓名">{{ model.zfPerson2Name }}</detail-list-item>
          <detail-list-item term="职务">{{ model.zfPerson2Dwzw }}</detail-list-item>
        </div>

      </detail-list>
         <a-divider style="margin: 16px 0" />
      <detail-list title="司机信息" :col="4">
        <div class="ant-row">
          <detail-list-item term="姓名">{{ model.driverName }}</detail-list-item>
          <detail-list-item term="性别">{{ model.driverSex }}</detail-list-item>
          <detail-list-item term="身份证">{{ model.driverIdCard }}</detail-list-item>
          <detail-list-item term="单位及职务">{{ model.driverDwzw }}</detail-list-item>
        </div>
        <div class="ant-row">
          <detail-list-item term="电话">{{ model.driverTel }}</detail-list-item>
          <detail-list-item term="所在地邮编">{{ model.driverPostcode }}</detail-list-item>
          <detail-list-item term="驾驶证号">{{ model.driverJszh }}</detail-list-item>
          <detail-list-item term="住址">{{ model.driverAddress }}</detail-list-item>
        </div>
         
      </detail-list>
         <a-divider style="margin: 16px 0" />
      <detail-list title="车主信息">
        <div class="ant-row">
          <detail-list-item term="企业名称">{{ model.czqyName }}</detail-list-item>
          <detail-list-item term="企业法人">{{ model.czqyLegalPerson }}</detail-list-item>
          <detail-list-item term="企业地址">{{ model.czqyAddress }}</detail-list-item>
        </div>
        <div class="ant-row">
          <detail-list-item term="企业电话">{{ model.czqyTel }}</detail-list-item>
          <detail-list-item term="车主姓名">{{ model.czName }}</detail-list-item>
          <detail-list-item term="车主性别">{{ model.czSex }}</detail-list-item>
         </div>
         <div class="ant-row">
          <detail-list-item term="车主身份证">{{ model.czIdcard }}</detail-list-item>
          <detail-list-item term="车主住址">{{ model.czAddress }}</detail-list-item>
          <detail-list-item term="车主电话">{{ model.czTel }}</detail-list-item>
         </div>
      </detail-list>


    </a-card>

    <!-- 操作 -->
    <!-- <a-card
      style="margin-top: 24px"
      :bordered="false"
      :tabList="tabList"
      :activeTabKey="activeTabKey"
      @tabChange="(key) => {this.activeTabKey = key}"
    >
      <a-table
        v-if="activeTabKey === '1'"
        :columns="operationColumns"
        :dataSource="operation1"
        :pagination="false"
      >
        <template
          slot="status"
          slot-scope="status">
          <a-badge :status="status | statusTypeFilter" :text="status | statusFilter"/>
        </template>
      </a-table>
      <a-table
        v-if="activeTabKey === '2'"
        :columns="operationColumns"
        :dataSource="operation2"
        :pagination="false"
      >
        <template
          slot="status"
          slot-scope="status">
          <a-badge :status="status | statusTypeFilter" :text="status | statusFilter"/>
        </template>
      </a-table>
      <a-table
        v-if="activeTabKey === '3'"
        :columns="operationColumns"
        :dataSource="operation3"
        :pagination="false"
      >
        <template
          slot="status"
          slot-scope="status">
          <a-badge :status="status | statusTypeFilter" :text="status | statusFilter"/>
        </template>
      </a-table>
    </a-card> -->

  </page-layout>
  </div>
</template>

<script>
  import { mixinDevice } from '@/utils/mixin.js'
  import PageLayout from '@/components/page/PageLayout'
  import DetailList from '@/components/tools/DetailList'
  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  const DetailListItem = DetailList.Item

  export default {
    name: "CaseListInfo",
    components: {
      PageLayout,
      DetailList,
      DetailListItem
    },
    mixins: [mixinDevice],
    props:{

    },
    data () {
      return {
        model:{},
        tabList: [
          {
            key: '1',
            tab: '最近超载记录'
          },
        ],
        visible:false,
        activeTabKey: '1',

        operationColumns: [
          {
            title: '车牌号',
            dataIndex: 'type',
            key: 'type'
          },
          {
            title: '站点',
            dataIndex: 'name',
            key: 'name'
          },
          {
            title: '重量',
            dataIndex: 'status',
            key: 'status',
            scopedSlots: { customRender: 'status' },
          },
          {
            title: '称重时间',
            dataIndex: 'updatedAt',
            key: 'updatedAt'
          },
          {
            title: '称重时间',
            dataIndex: 'updatedAt',
            key: 'updatedAt'
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark'
          }
        ],
      
     
      }
    },
    filters: {
      statusFilter(status) {
        const statusMap = {
          'agree': '成功',
          'reject': '驳回'
        }
        return statusMap[status]
      },
      statusTypeFilter(type) {
        const statusTypeMap = {
          'agree': 'success',
          'reject': 'error'
        }
        return statusTypeMap[type]
      }
    },
    computed: {
    },
    created(){
      console.log(this.model);
    },
    methods: {
      show(record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
    }
  }
</script>

<style lang="less" scoped>

  .detail-layout {
    margin-left: 44px;
  }
  .text {
    color: rgba(0, 0, 0, .45);
  }

  .heading {
    color: rgba(0, 0, 0, .85);
    font-size: 20px;
  }

  .no-data {
    color: rgba(0, 0, 0, .25);
    text-align: center;
    line-height: 64px;
    font-size: 16px;

    i {
      font-size: 24px;
      margin-right: 16px;
      position: relative;
      top: 3px;
    }
  }

  .mobile {
    .detail-layout {
      margin-left: unset;
    }
    .text {

    }
    .status-list {
      text-align: left;
    }
  }
</style>
