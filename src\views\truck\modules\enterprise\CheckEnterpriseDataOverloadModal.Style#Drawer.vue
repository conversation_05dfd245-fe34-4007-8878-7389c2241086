<template>
  <a-drawer :title="title" :width="width" placement="right" :closable="false" @close="close" destroyOnClose
    :visible="visible">
    <!-- <check-enterprise-data-overload-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit" normal></check-enterprise-data-overload-form> -->
    <check-enterprise-data-overload-detail ref="detailForm" v-if="detailVisible" @ok="submitCallback"></check-enterprise-data-overload-detail>
    <check-enterprise-data-overload-abnormal-form ref="abnormalForm" v-if="abnormalVisible" @ok="submitCallback">
    </check-enterprise-data-overload-abnormal-form>
    <div class="drawer-footer">
      <a-button @click="handleCancel" style="margin-bottom: 0;">关闭</a-button>
      <a-button v-if="!disableSubmit" @click="handleOk" type="primary" style="margin-bottom: 0;">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>

// import CheckEnterpriseDataOverloadForm from './CheckEnterpriseDataOverloadForm'
import CheckEnterpriseDataOverloadDetail from './CheckEnterpriseDataOverloadDetail'
import CheckEnterpriseDataOverloadAbnormalForm from './CheckEnterpriseDataOverloadAbnormalForm'
export default {
  name: 'CheckEnterpriseDataOverloadModal',
  components: {
    CheckEnterpriseDataOverloadDetail,
    CheckEnterpriseDataOverloadAbnormalForm
  },
  data() {
    return {
      title: "操作",
      width: 1024,
      visible: false,
      disableSubmit: false,
      detailVisible: false,
      abnormalVisible: false
    }
  },
  methods: {
    setVisible() {
      this.visible = true
      this.detailVisible = false
      this.abnormalVisible = false
    },
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add();
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record);
      });
    },
    show(record) {
      this.setVisible();
      this.detailVisible = true;
      this.$nextTick(() => {
        this.$refs.detailForm.show(record);
      });
    },
    abnormalAdd(record) {
      this.setVisible();
      this.abnormalVisible = true
      this.$nextTick(() => {
        this.$refs.abnormalForm.add(record);
      });
    },
    close() {
      this.$emit('close');
      this.visible = false;
    },
    submitCallback() {
      this.$emit('ok');
      this.visible = false;
    },
    handleOk() {
       if(this.addVisible){
          this.$refs.addForm.submitForm();
        }
        if(this.abnormalVisible){
          this.$refs.abnormalForm.submitForm();
        }
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style lang="less" scoped>
/** Button按钮间距 */
.ant-btn {
  margin-left: 30px;
  margin-bottom: 30px;
  float: right;
}

.drawer-footer {
  position: absolute;
  bottom: -8px;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 2px 2px;
}
</style>