<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="4" :lg="6" :md="8" :sm="24">
            <a-form-item label="年份">
              <a-date-picker
                mode="year"
                v-decorator="['year']"
                v-model="year"
                placeholder="请输入年份"
                format="YYYY"
                style="width: 200px"
                :open="yearShowOne"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              />
            </a-form-item>
          </a-col>

          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <a-row>
      <a-col>
        <a-table v-bind="tableProps" style="margin-top: 20px">
          <span slot="action" slot-scope="text, record">
            <a @click="handleMonthReport(record)">月报详情</a>
            <a-divider type="vertical" />
            <a @click="regeneration(record)">重新生成</a>
          </span>
        </a-table>
      </a-col>
    </a-row>
  </a-card>
</template>


<script>
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { getAction} from '@/api/manage'
  import moment from 'moment'
  import store from '@/store'
  export default {
    name: 'SiteMonthList',
    mixins:[JeecgListMixin, mixinDevice],
    data() {
      return {
        columns: [
          {
            title: '#',
            width: '180px',
            align: 'center',
            dataIndex: 'rowIndex',
            customRender: function (text, r, index) {
              return (text !== '总计') ? (parseInt(index) + 1) : text
            }
          },
          {
            title: '月份',
            dataIndex: 'time',
            width: 180,
            key:"time"
          },
          {
            title: '过车数',
            dataIndex: 'carNum',
            width: 180,
            key:"carNum"
          },
          {
            title: '超载数',
            dataIndex: 'overNum',
            width: 180,
             key:"overNum"
          },
          {
            title: '大于一吨',
            dataIndex: 'gtNum',
            width: 180,
            key:"gtNum"
          },
           {
            title: '小于一吨',
            dataIndex: 'ltNum',
            width: 180,
             key:"ltNum"
          },
          {
            title: '严重超载',
            dataIndex: 'severeNum',
            width: 180,
             key:"severeNum"
          },
        {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
         url: {
          list: "/statistics/statisticsSite/siteMonth",
        },
        /* 分页参数 */
        ipagination:{
          current: 1,
          pageSize: 10,
        },
        year: moment(new Date()).format('YYYY-MM-DD'),
        dataSource: [
        ],
        newArr:[],
        newDataSource:[],
        footerDataSource: [],
        yearShowOne:false
      }
    },
    created() {
      this.year = moment(new Date()).format('YYYY-MM-DD');
      this.loadData(this.year);
    },
    computed:{
      // 数据表格的固定属性
      tableProps(){
        let tableProps = {
          size: 'middle',
          rowKey:'rowIndex',
          columns: this.columns,
          scroll: {x: true},
        }
        let renderFooter = this.footerDataSource.length === 0 ? null : () => this.renderTableFooter(tableProps)
        return {
          ...tableProps,
          ref: 'table',
          class: 'chart-data-list',
          pagination:false,
          columns: this.columns,
          dataSource: this.dataSource,
          footer: renderFooter,
        }
      },

    },
    mounted() {
      this.tableAddTotalRow(this.columns, this.dataSource)
      /*新增分页合计方法*/
      this.dataHandling()
    },
    watch:{
      //当合计行变化时，绑定滚动条
      'footerDataSource': {
        async handler(dataSource) {
          // 当底部合计行有值，并且显示出来时，再同步滚动条
          if (dataSource && dataSource.length > 0) {
            await this.$nextTick()
            // 同步表与footer滚动
            let dom = this.$refs.table.$el.querySelectorAll('.ant-table-body')[0]
            let footerDom = this.$refs.footerTable.$el.querySelectorAll('.ant-table-body')[0]
            dom.addEventListener(
                'scroll',
                () => {
                  footerDom.scrollLeft = dom.scrollLeft
                },
                true,
            )
          }
        },
        //update-end---author:wangshuai ---date:20220209  for：[JTC-494]常用示例->表格合计写法改成新的写法------------
      }
    },
    methods: {
        moment,
        /** 加载数据 */
        loadData(year){
      
            var params = {
                time: this.year
            }
            getAction(this.url.list, params).then(res=>{
                if(res.success){
                    this.dataSource = res.result
                     this.dataHandling()
                }
                 
            });
        },
        searchQuery(){
           this.loadData(this.year);
        },
        searchReset(){
            this.year = moment(new Date()).format('YYYY-MM-DD');
            this.loadData(this.year);
        },
        openChangeOne(status) {
            if (status) {
                this.yearShowOne = true;
            }
        },
        // 得到年份选择器的值
        panelChangeOne(value) {
        this.yearShowOne = false;
        this.year = moment(value).format("YYYY-MM-DD");;
        },
      /** 表格增加合计行 */
      tableAddTotalRow(columns, dataSource) {
          
        let numKey = 'rowIndex'
        let totalRow = { [numKey]: '合计' }
        columns.forEach(column => {
          let { key, dataIndex } = column
          if (![key, dataIndex].includes(numKey)) {

            let total = 0
            dataSource.forEach(data => {
              total += /^\d+\.?\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN
              console.log(data[dataIndex], ':', (/^\d+\.?\d?$/.test(data[dataIndex]) ? Number.parseInt(data[dataIndex]) : Number.NaN))
            })

            if (Number.isNaN(total)) {
              total = '-'
            }
            totalRow[dataIndex] = total
          }
        })

        dataSource.push(totalRow)
      },
      //update-begin---author:wangshuai ---date:20220209  for：[JTC-494]常用示例->表格合计写法改成新的写法------------
      /*如果分页走这个方法*/
      dataHandling() {
          console.log("datasource",this.dataSource);
        //根据当前页数和每页显示条数分割数组
        let arrs = [];
        //如果pageNo不是-1（不分页）,那么需要对数据进行分页计算
        arrs = this.dataSource
        let newDataSource=[];
        let newArr= { };
        newArr.rowIndex="总计"
        let carNum=0;
        let overNum=0;
        let gtNum=0;
        let ltNum=0;
        let severeNum=0;
        //每一项的数值相加
        for (let j=0;j<arrs.length;j++){
          carNum+=arrs[j].carNum;
          overNum+=arrs[j].overNum;
          gtNum+=arrs[j].gtNum;
          ltNum+=arrs[j].ltNum;
          severeNum+=arrs[j].severeNum;
        }
        newArr.carNum=carNum;
        newArr.overNum=overNum;
        newArr.gtNum=gtNum;
        newArr.ltNum=ltNum;
        newArr.severeNum=severeNum;
        newDataSource.push(newArr);
        console.log(arrs);
        console.log(newDataSource);
        //给foot底部数组赋值
        this.footerDataSource = newDataSource;
      },
      // 渲染表格底部合计行
      renderTableFooter(tableProps) {
        let h = this.$createElement
        return h('a-table', {
          ref: 'footerTable',
          props: {
            ...tableProps,
            pagination: false,
            dataSource: this.footerDataSource,
            showHeader: false,
          },
        })
        //update-end---author:wangshuai ---date:20220209  for：[JTC-494]常用示例->表格合计写法改成新的写法------------
      },
      handleMonthReport(record){
          let token = store.getters.token
          window.open(window._CONFIG["domianURL"]+"/jmreport/view/683514357397331968?token="+token+"&time="+record.time);
      },
      regeneration(record){

      }
    }
  }
</script>

<style scoped lang="less">
/deep/ .chart-data-list .ant-table-footer .ant-table-body {
  overflow: hidden !important;
}
/deep/ .ant-table-footer {
  padding: 0;
}
</style>