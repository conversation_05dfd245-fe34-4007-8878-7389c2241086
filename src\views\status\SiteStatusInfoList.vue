<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="站点">
              <j-dict-select-tag type="list" v-model="siteType" dictCode="site_type"
                placeholder="请选择站点" />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="站点">
              <j-dict-select-tag type="list" v-model="queryParam.siteCode" dictCode="base_site,name,code"
                placeholder="请选择站点" />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="站点状态">
              <j-dict-select-tag placeholder="请选择站点状态" v-model="queryParam.siteStatus" dictCode="online_status" />
            </a-form-item>
          </a-col>
           <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="上传状态">
              <j-dict-select-tag placeholder="请选择上传状态" v-model="queryParam.isUpload" dictCode="upload_status" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->



    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
            selectedRowKeys.length
        }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <site-status-info-modal ref="modalForm" @ok="modalFormOk"></site-status-info-modal>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SiteStatusInfoModal from './modules/SiteStatusInfoModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'

export default {
  name: 'SiteStatusInfoList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    SiteStatusInfoModal
  },
  data() {
    return {
      description: '站点状态信息表管理页面',
      siteType:"1",
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function (t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '站点名称',
          align: "center",
          dataIndex: 'siteCode_dictText'
        },
        {
          title: '站点状态',
          align: "center",
          dataIndex: 'siteStatus_dictText'
        },
        {
          title: '消息发送时间',
          align: "center",
          dataIndex: 'sendTime'
        },
        {
          title: '上传状态',
          align: "center",
          dataIndex: 'isUpload_dictText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          fixed: "right",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: "/status/siteStatusInfo/list",
        delete: "/status/siteStatusInfo/delete",
        deleteBatch: "/status/siteStatusInfo/deleteBatch",
        exportXlsUrl: "/status/siteStatusInfo/exportXls",
        importExcelUrl: "status/siteStatusInfo/importExcel",

      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList();
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    initDictConfig() {
    },
    getSuperFieldList() {
      let fieldList = [];
      fieldList.push({ type: 'string', value: 'uniqueId', text: '唯一标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'siteCode', text: '站点标识', dictCode: '' })
      fieldList.push({ type: 'int', value: 'siteStatus', text: '站点状态', dictCode: 'online_status' })
      fieldList.push({ type: 'string', value: 'sendTime', text: '消息发送时间', dictCode: '' })
      this.superFieldList = fieldList
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>