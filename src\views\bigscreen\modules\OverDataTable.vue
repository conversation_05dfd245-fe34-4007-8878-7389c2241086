<template>
  <div class="container">
    <dv-border-box-8 class="container_box">
      <dv-scroll-board :config="config" class="table_list" ref="scrollBoard" />
    </dv-border-box-8>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
export default {
  name: 'OverDataTable',
  data() {
    return {
      config: {
        header: ['站点', '车牌号', '轴数', '轴型', '重量', '限重', '超载', '超载率', '检测类型', '磅型', '时间'],
        headerBGC: '#0a1545',
        oddRowBGC: '#0a1545',
        evenRowBGC: '#0a1545',
        rowNum: 5,
        data: [],
        headerHeight: 35,
        align: [
          'center',
          'center',
          'center',
          'center',
          'center',
          'center',
          'center',
          'center',
          'center',
          'center',
          'center',
          'left',
        ],
        carousel: 'single',
        hoverPause: true,
      },
      url: {
        dataUrl: '/bigScreen/siteRealOverData',
      },
      timer: '',
      last_time: '',
    }
  },
  created() {
    this.getData()
  },
  mounted() {
    this.timer = setInterval(this.doUpdate, 100000)
  },
  methods: {
    getData() {
      getAction(this.url.dataUrl).then((res) => {
        if (res.success) {
          this.config.data = this.convertData(res.result);
          this.config = { ...this.config }
          this.last_time = this.config.data.slice(-1)[0].createTime
          console.log(this.last_time,'===>this.last_time')
        }
      })
    },
    convertData(data) {
      var row = [];
      data.forEach(item => {
        var arr = [];
        arr.push(item.siteName);
        arr.push(item.vehicleNo);
        arr.push(item.axle);
        arr.push(item.axleType);
        arr.push(item.total);
        arr.push(item.limitWeight);
        arr.push(item.overWeight);
        arr.push(item.overRate);
        arr.push(item.checkType);
        arr.push(item.poundType);
        arr.push(item.createTime);
        row.push(arr);
      })
      return row;
    },
    
    doUpdate() {
      getAction(this.url.dataUrl, ).then((res) => {
        if (res.success) {
          console.log(res.result.length)
          if (res.result.length > 0) {
            this.config.data.push(this.convertData(res.result))
            // this.$refs.scrollBoard.updateRows(this.convertData(res.result), 1)
            // this.$refs['scrollBoard'].updateRows(this.convertData(res.result), 1);
          }
        }
      })
      // this.$refs['scrollBoard'].updateRows(rows, index)
    },
  },
}
</script>
<style lang="less" scoped>
.container {
  position: relative;

  // background: red;
  .container_box {
    width: 95%;
    height: 95%;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 10px;

    .table_list {
      width: 100%;
      height: 100%;
    }

    /deep/ .header {
      display: -ms-flexbox;
      display: flex;
      -ms-flex: 1;
      flex: 1;
      -ms-flex-direction: row;
      flex-direction: row;
      -ms-flex-pack: justify;
      justify-content: space-between;
      margin: 0px;
    }
  }
}

.dv-scroll-board .header .header-item {
  text-align: left;
  padding: 0 10px;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s;
  font-size: 12px;
}

::v-deep .header-item,
::v-deep .ceil {
  text-align: center;
  width: 250px !important;
}

::v-deep .header-item:nth-child(1),
::v-deep .ceil:nth-child(1) {
  text-align: center;
  width: 350px !important;
}
::v-deep .header-item:nth-child(2),
::v-deep .ceil:nth-child(2) {
  text-align: center;
  width: 300px !important;
}
::v-deep .header-item:nth-child(3),
::v-deep .ceil:nth-child(3) {
  text-align: center;
  width: 140px !important;
}
::v-deep .header-item:nth-child(4),
::v-deep .ceil:nth-child(4) {
  text-align: center;
  width: 140px !important;
}
::v-deep .header-item:nth-child(5),
::v-deep .ceil:nth-child(5) {
  text-align: center;
  width: 200px !important;
}
::v-deep .header-item:nth-child(6),
::v-deep .ceil:nth-child(6) {
  text-align: center;
  width: 200px !important;
}
::v-deep .header-item:nth-child(7),
::v-deep .ceil:nth-child(7) {
  text-align: center;
  width: 200px !important;
}
::v-deep .header-item:nth-child(8),
::v-deep .ceil:nth-child(8) {
  text-align: center;
  width: 200px !important;
}
::v-deep .header-item:nth-child(9),
::v-deep .ceil:nth-child(9) {
  text-align: center;
  width: 250px !important;
}
::v-deep .header-item:nth-child(10),
::v-deep .ceil:nth-child(10) {
  text-align: center;
  width: 220px !important;
}
::v-deep .header-item:nth-child(11),
::v-deep .ceil:nth-child(11) {
  text-align: center;
  width: 500px !important;
}
</style>