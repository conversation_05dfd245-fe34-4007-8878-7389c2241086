<template>
    <div id="top-header">
        <h1>晋城市治理超载超限大屏系统</h1>
        <div class="time"><span>{{ this.datetime }}</span></div>
    </div>
</template>
  
<script>
export default {
    name: 'TopHeader',
    data() {
        return {
            time: '',
            datetime: ''
        }
    },
    mounted() {
        this.dayjs.locale('zh-cn')
        /* 每秒刷新 */
        this.time = setInterval(() => {
            this.datetime = this.dayjs().format('YYYY年MM月DD日 dddd HH时mm分ss秒')
            //this.array = res.data.map((item) =>  
            //this.dayjs(item.stateTime).format('HH:mm')).reverse()
        }, 1000)
    },
    beforeDestroy() {
        /* 离开页面前销毁定时器 */
        if (this.time) {
            clearInterval(this.time);
        }
    },
    methods: {
        getWeek(num) {
            var datas = dayjs().day()
            var week = ['日', '一', '二', '三', '四', '五', '六']
            return '星期' + week[datas]
        },
    }

}
</script>
  
<style lang="less">
#top-header {
    // height: 1.05rem;
    background: url(~@/assets/screen/head_bg.png) no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    height: 5rem;

    h1 {
        color: #fff;
        text-align: center;
        font-size: 2.0rem;
        line-height: 4.75rem;
        font-weight:900;
    }

    .time {
        position: absolute;
        right: 1.75rem;
        top: 0;
        line-height: 4.5rem;

        span {
            color: rgba(255, 255, 255, .7);
            font-size: 1rem;
            padding-right: .1rem;
        }
    }
}</style>
  