<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="站点">
              <j-dict-select-tag placeholder="请选择站点" v-model="queryParam.siteCode" dictCode="base_site,name,code" />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="车牌号码">
              <a-input placeholder="请输入车牌号码" v-model="queryParam.hphm"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="县区">
                <j-area-linkage type="cascader" v-model="queryParam.xzqh" placeholder="请选择省市区" />
              </a-form-item>
            </a-col>
            <a-col :xl="7" :lg="11" :md="12" :sm="24">
              <a-form-item label="违法时间">
                <j-date-range v-model="date" style="width:100%" @change="dateRangeChange"></j-date-range>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button type="primary" icon="download" @click="handleExportXls('交警电子抓拍数据')">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery">
      </j-super-query>
      <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown> -->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" class="j-table-force-nowrap"
        @change="handleTableChange">
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic" />
        </template>
        <template slot="plateSlot" slot-scope="text, record">
          <plate :plate="text" :color="record.hpys"></plate>
        </template>
        <template slot="pcaSlot" slot-scope="text">
          <div>{{ getPcaText(text) }}</div>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <!-- <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                 <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown> -->
        </span>
      </a-table>
    </div>

    <police-data-modal ref="modalForm" @ok="modalFormOk"></police-data-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import PoliceDataModal from './modules/PoliceDataModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import Plate from '@/components/plate/Plate'
import Area from '@/components/_util/Area'
import JDateRange from '@/components/jeecg/JDateRange'
export default {
  name: 'PoliceDataList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    PoliceDataModal,
    Plate,
    JDateRange
  },
  data() {
    return {
      description: '交警电子抓拍数据管理页面',
      date:[],
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '序号',
          align: 'center',
          dataIndex: 'xh',
        },

        {
          title: '站点简称',
          align: 'center',
          dataIndex: 'kkmc',
        },
        {
          title: '检测方向',
          align: 'center',
          dataIndex: 'cdmc',
        },
        {
          title: '车辆分类',
          align: 'center',
          dataIndex: 'clfl',
        },
        // {
        //   title: '号牌种类',
        //   align: 'center',
        //   dataIndex: 'hpzl',
        // },
        {
          title: '车牌号码',
          align: 'center',
          dataIndex: 'hphm',
          scopedSlots: { customRender: 'plateSlot' },
        },
        {
          title: '违法地行政区划',
          align: 'center',
          dataIndex: 'xzqh',
          scopedSlots: { customRender: 'pcaSlot' },
        },
        {
          title: '违法地址',
          align: 'center',
          dataIndex: 'wfdz',
        },
        {
          title: '违法时间',
          align: 'center',
          dataIndex: 'wfsj',
          // sorter: true, 
          sortDirections: ['descend', 'ascend', 'descend'],
          // 配置默认是倒序
          defaultSortOrder: 'descend',
          sorter: (a, b) => { return a.wfsj> b.wfsj? 1 : -1 },
        },
        // {
        //   title: '违法行为',
        //   align: 'center',
        //   dataIndex: 'wfxw',
        // },
        {
          title: '行驶速度',
          align: 'center',
          dataIndex: 'clsd',
        },
        {
          title: '违法行为代码',
          align: 'center',
          dataIndex: 'wfxwdm',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/police/policeData/list',
        delete: '/police/policeData/delete',
        deleteBatch: '/police/policeData/deleteBatch',
        exportXlsUrl: '/police/policeData/exportXls',
        importExcelUrl: 'police/policeData/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.pcaData = new Area()
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() { },
    getPcaText(code) {
      return this.pcaData.getText(code)
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'xh', text: '序号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'siteCode', text: '站点编码', dictCode: 'base_site,name,code' })
      fieldList.push({ type: 'string', value: 'kkmc', text: '站点简称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'cdmc', text: '检测方向', dictCode: '' })
      fieldList.push({ type: 'string', value: 'sbbh', text: '设备编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'clfl', text: '车辆分类', dictCode: '' })
      fieldList.push({ type: 'string', value: 'hpzl', text: '号牌种类', dictCode: '' })
      fieldList.push({ type: 'string', value: 'hphm', text: '车牌号码', dictCode: '' })
      fieldList.push({ type: 'int', value: 'hpys', text: '车牌颜色', dictCode: '' })
      fieldList.push({ type: 'string', value: 'xzqh', text: '违法地行政区划', dictCode: '' })
      fieldList.push({ type: 'string', value: 'wfdz', text: '违法地址', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'wfsj', text: '违法时间' })
      fieldList.push({ type: 'string', value: 'wfxw', text: '违法行为', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zpsl', text: '照片数量', dictCode: '' })
      fieldList.push({ type: 'string', value: 'tplj', text: '车牌图片', dictCode: '' })
      fieldList.push({ type: 'string', value: 'clsd', text: '编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'wfxwdm', text: '违法行为代码', dictCode: '' })
      this.superFieldList = fieldList
    },
     dateRangeChange(dateStr) {
      this.date = dateStr;
      this.queryParam.wfsj_begin = dateStr[0]
      this.queryParam.wfsj_end = dateStr[1]
    }
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>