<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业名称">
              <a-input placeholder="请输入企业名称" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业编码">
              <a-input placeholder="请输入企业编码" v-model="queryParam.code"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="行政编码">
                <j-area-linkage type="cascader" v-model="queryParam.distCode" placeholder="请选择省市区"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="企业类型">
                <j-dict-select-tag placeholder="请选择企业类型" v-model="queryParam.type" dictCode="enterprise_type_new"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="数据状态">
                <j-dict-select-tag placeholder="请选择数据状态" v-model="queryParam.dataStatus" dictCode="data_status"/>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="企业状态">
                <j-dict-select-tag placeholder="请选择企业状态" v-model="queryParam.status" dictCode="enterprise_status"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('新企业信息表')">导出</a-button>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="pcaSlot" slot-scope="text">
          <div>{{ getPcaText(text) }}</div>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <template slot="customRenderDataStatus" slot-scope="status">
          <a-tag v-if="status==1" color="green">正常</a-tag>
          <a-tag v-if="status==0" color="red">异常</a-tag>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
        </span>

      </a-table>
    </div>  
    <CustomDetail ref="detailRef"/>
    <base-enterprise-new-modal ref="modalForm" @ok="modalFormOk"></base-enterprise-new-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseEnterpriseNewModal from './modules/BaseEnterpriseNewModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import Area from '@/components/_util/Area'
  import CustomDetail from '@/components/CustomDetail'

  export default {
    name: 'BaseEnterpriseNewList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      BaseEnterpriseNewModal,
      CustomDetail
    },
    data () {
      return {
        description: '新企业信息表管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'企业名称',
            align:"center",
            dataIndex: 'shortName'
          },
          {
            title:'企业编码',
            align:"center",
            dataIndex: 'code'
          },
          {
            title:'行政编码',
            align:"center",
            dataIndex: 'distCode',
            scopedSlots: {customRender: 'pcaSlot'}
          },
          {
            title:'企业类型',
            align:"center",
            dataIndex: 'type_dictText'
          },
          {
            title:'是否重点企业',
            align:"center",
            dataIndex: 'isFocus_dictText',
          },
          {
            title:'数据状态',
            align:"center",
            dataIndex: 'dataStatus',
            scopedSlots: { customRender: 'customRenderDataStatus' },
          },
          {
            title:'企业状态',
            align:"center",
            dataIndex: 'status_dictText'
          },
          {
            title:'最后数据时间',
            align:"center",
            dataIndex: 'lastDataTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/info/baseEnterprise/list",
          delete: "/info/baseEnterprise/delete",
          deleteBatch: "/info/baseEnterprise/deleteBatch",
          exportXlsUrl: "/info/baseEnterprise/exportXls",
          importExcelUrl: "/info/baseEnterprise/importExcel",
          
        },
        dictOptions:{},
        pcaData:'',
        superFieldList:[],
        detailData:{},
      }
    },
    created() {
      this.pcaData = new Area()
      this.$set(this.dictOptions, 'hasWeighting', [{text:'是',value:'Y'},{text:'否',value:'N'}])
      this.$set(this.dictOptions, 'hasCamera', [{text:'是',value:'Y'},{text:'否',value:'N'}])
      this.$set(this.dictOptions, 'hasNetwork', [{text:'是',value:'Y'},{text:'否',value:'N'}])
      this.$set(this.dictOptions, 'hasYundan', [{text:'是',value:'Y'},{text:'否',value:'N'}])
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      handleDetail(record){
        this.$refs.detailRef.openDrawer(record,{
          titleValue:'企业基础详情',
          detailSchemaValue:this.superFieldList,
          columnValue:this.columns,
          tableApi:()=>this.$api.info.baseEnterpriseDetail(record.id)
        })
        console.log(record,'===<record')
      },
      getPcaText(code){
        return this.pcaData.getText(code);
      },
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'name',text:'企业名称',dictCode:''})
        fieldList.push({type:'string',value:'code',text:'企业编码',dictCode:''})
        fieldList.push({type:'string',value:'shortName',text:'简称',dictCode:''})
        fieldList.push({type:'string',value:'sourceCode',text:'信用编码',dictCode:''})
        fieldList.push({type:'pca',value:'distCode',text:'行政编码'})
        fieldList.push({type:'string',value:'type',text:'企业类型',dictCode:'enterprise_type_new'})
        fieldList.push({type:'string',value:'address',text:'地址',dictCode:''})
        fieldList.push({type:'string',value:'postCode',text:'邮编',dictCode:''})
        fieldList.push({type:'string',value:'personInChange',text:'负责人',dictCode:''})
        fieldList.push({type:'string',value:'contactor',text:'联系人',dictCode:''})
        fieldList.push({type:'string',value:'telephone',text:'联系电话',dictCode:''})
        fieldList.push({type:'string',value:'chuanZhen',text:'传真',dictCode:''})
        fieldList.push({type:'switch',value:'hasWeighting',text:'是否安装称重'})
        fieldList.push({type:'switch',value:'hasCamera',text:'是否安装抓拍'})
        fieldList.push({type:'switch',value:'hasNetwork',text:'是否联网'})
        fieldList.push({type:'switch',value:'hasYundan',text:'是否使用电子运单'})
        fieldList.push({type:'list_multi',value:'hl',text:'主要货物类型',dictTable:"base_goods", dictText:'name', dictCode:'code'})
        fieldList.push({type:'double',value:'longitude',text:'经度',dictCode:''})
        fieldList.push({type:'string',value:'latitude',text:'纬度',dictCode:''})
        fieldList.push({type:'string',value:'ministerialOerloadCode',text:'部级编码',dictCode:''})
        fieldList.push({type:'switch',value:'isFocus',text:'是否重点企业'})
        fieldList.push({type:'date',value:'focusDate',text:'列为重点日期'})
        fieldList.push({type:'string',value:'dataStatus',text:'数据状态',dictCode:'data_status'})
        fieldList.push({type:'string',value:'status',text:'企业状态',dictCode:'enterprise_status'})
        fieldList.push({type:'datetime',value:'reviewTime',text:'审核时间'})
        fieldList.push({type:'string',value:'izReview',text:'是否审核',dictCode:''})
        fieldList.push({type:'string',value:'reviewBy',text:'审核人',dictCode:''})
        fieldList.push({type:'string',value:'reviewStopRemark',text:'拒绝理由',dictCode:''})
        fieldList.push({type:'datetime',value:'lastDataTime',text:'最后数据时间'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>