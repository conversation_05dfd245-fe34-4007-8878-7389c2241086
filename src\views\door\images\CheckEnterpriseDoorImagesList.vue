<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button type="primary" icon="download" @click="handleExportXls('企业出入口图片表')">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
<!-- 
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown> -->
        </span>

      </a-table>
    </div>

    <check-enterprise-door-images-modal ref="modalForm" @ok="modalFormOk"></check-enterprise-door-images-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import CheckEnterpriseDoorImagesModal from './modules/CheckEnterpriseDoorImagesModal'

  export default {
    name: 'CheckEnterpriseDoorImagesList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      CheckEnterpriseDoorImagesModal
    },
    data () {
      return {
        description: '企业出入口图片表管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'出入口编码',
            align:"center",
            dataIndex: 'checkDoorId'
          },
          {
            title:'站点编码',
            align:"center",
            dataIndex: 'enterpriseCode'
          },
          {
            title:'设备编码',
            align:"center",
            dataIndex: 'equCode'
          },
          {
            title:'检查时间',
            align:"center",
            dataIndex: 'checkTime',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'车牌号',
            align:"center",
            dataIndex: 'vehicleNo'
          },
          {
            title:'文件名称',
            align:"center",
            dataIndex: 'fileName'
          },
          {
            title:'文件大小',
            align:"center",
            dataIndex: 'dataLength'
          },
          {
            title:'数据类型',
            align:"center",
            dataIndex: 'dataType'
          },
          {
            title:'检测方向',
            align:"center",
            dataIndex: 'checkDir'
          },
          {
            title:'文件地址',
            align:"center",
            dataIndex: 'dataUrl',
            scopedSlots: {customRender: 'imgSlot'}
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/door/checkEnterpriseDoorImages/list",
          delete: "/door/checkEnterpriseDoorImages/delete",
          deleteBatch: "/door/checkEnterpriseDoorImages/deleteBatch",
          exportXlsUrl: "/door/checkEnterpriseDoorImages/exportXls",
          importExcelUrl: "/door/checkEnterpriseDoorImages/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'checkDoorId',text:'出入口编码',dictCode:''})
        fieldList.push({type:'string',value:'enterpriseCode',text:'站点编码',dictCode:''})
        fieldList.push({type:'string',value:'equCode',text:'设备编码',dictCode:''})
        fieldList.push({type:'date',value:'checkTime',text:'检查时间'})
        fieldList.push({type:'string',value:'vehicleNo',text:'车牌号',dictCode:''})
        fieldList.push({type:'string',value:'fileName',text:'文件名称',dictCode:''})
        fieldList.push({type:'int',value:'dataLength',text:'文件大小',dictCode:''})
        fieldList.push({type:'string',value:'dataType',text:'数据类型',dictCode:''})
        fieldList.push({type:'string',value:'checkDir',text:'检测方向',dictCode:''})
        fieldList.push({type:'string',value:'dataUrl',text:'文件地址',dictCode:''})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>