<template>
    <div class="itemBody">
      <itemTitle :icon="icon" :title="title"></itemTitle>
      <div class="itemMain">
        <slot></slot>
      </div>
      <bg></bg>
    </div>
  </template>
  
  <script>
  import itemTitle from "./itemTitle";
  import bg from './bg.vue'
  import icon from '@/assets/screen/divbox14/icon_x1.png'
  export default {
    name: "divbox14",
    data() {
      return {}
    },
    components: {
      itemTitle,
      bg
    },
    props: {
      title: {
        type: String,
        default() {
          return '标题';
        }
      },
      icon: {
        type: String,
        default() {
          return icon;
        }
      },
    },
    watch: {},
    mounted() {
      var that = this;
    },
  }
  </script>
  
  <style lang="scss" scoped>
  .itemBody {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: nowrap;
    flex-direction: column;
    z-index: 1;
    align-content: flex-start;
  
    span {
      font-family: PangMenZhengDao;
      margin-left: .75rem /* 12/16 */;
      font-size: 22px;
      font-weight: 400;
      padding-top: .5rem /* 8/16 */;
      color: #FFFFFF;
    }
  }
  
  .itemMain {
    position: relative;
    width: 100%;
    height:  4.375rem /* 70/16 */
  }
  </style>