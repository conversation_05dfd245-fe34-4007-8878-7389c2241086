<template>
  <j-modal :title="title" :width="width" :visible="visible" switchFullscreen @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }" @cancel="handleCancel" cancelText="关闭">
    <blacklist-car-form ref="realForm" v-if="visibleForm" @ok="submitCallback" :disabled="disableSubmit">
    </blacklist-car-form>
    <blacklist-car-info-list ref="realInfo" v-if="visibleInfo" :disabled="disableSubmit"></blacklist-car-info-list>
  </j-modal>
</template>

<script>

import BlacklistCarForm from './BlacklistCarForm'
import BlacklistCarInfoList from './BlacklistCarInfoList'
export default {
  name: 'BlacklistCarModal',
  components: {
    BlacklistCarForm,
    BlacklistCarInfoList
  },
  data() {
    return {
      title: '',
      width: 1024,
      visible: false,
      disableSubmit: false,
      visibleForm: false,
      visibleInfo: false
    }
  },
  methods: {
    setVisible(){
      this.visibleForm=false,
      this.visibleInfo=false
    },
    add() {
      this.setVisible();
      this.visible = true
      this.visibleForm = true
      this.$nextTick(() => {
        this.$refs.realForm.add();
      })
    },
    edit(record) {
      this.setVisible();
      this.visible = true
      this.visibleForm = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record);
      })

    },
    showInfo(record) {
      this.setVisible();
      this.visible = true;
      this.visibleInfo = true;
      console.log(this);
      this.$nextTick(() => {
        this.$refs.realInfo.show(record);
      })
      // console.log(this.visible, this.visibleForm);
    },
    close() {
      this.$emit('close');
      this.visible = false;
      this.setVisible();
    },
    handleOk() {
      this.$refs.realForm.submitForm();
    },
    submitCallback() {
      this.$emit('ok');
      this.visible = false;
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>