<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="formBigCar" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
           <a-col :span="12">
             <a-form-model-item label="站点名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteName">
              <j-dict-select-tag type="list" v-model="model.siteCode" dictCode="base_site,name,code" placeholder="请选择站点名称"  :disabled="isDisabled"/>
            </a-form-model-item>
          </a-col>
           <a-col :span="12">
             <div id="bigPlateInput">
              <a-form-model-item label="车牌号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNo">
                <a-input v-model="model.vehicleNo" placeholder="请输入车牌号码" @focus="getFocus"></a-input>
                <transition name="info">
                    <div style="width: 350px;height: 175px;background: red;z-index: 20;position: absolute;left:-30px" v-show="isshow">
                        <div id="single-keyboard-box">
                            <mixed-keyboard ref="keyboard" :args="args"
                              :callbacks="callbacks">
                            </mixed-keyboard>
                        </div>
                    </div>
                </transition>
              </a-form-model-item>
            </div>
           </a-col>
            <a-col :span="12">
              <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateColor">
                <j-dict-select-tag type="list" v-model="model.plateColor" dictCode="plate_color" placeholder="请选择车牌颜色" />
              </a-form-model-item>
            </a-col>
             <a-col :span="12">
                <a-form-model-item label="总重(Kg)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="total">
                    <a-input-number v-model="model.total" placeholder="请输入总重" style="width: 100%" :disabled="isDisabled" addon-after="KG" />
                </a-form-model-item>
             </a-col>
              <a-col :span="12">
                <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
                  <j-dict-select-tag type="list" v-model="model.axles" dictCode="axles" placeholder="请选择轴数" />
                </a-form-model-item>
              </a-col>
               <a-col :span="12">
                <a-form-model-item label="大件运输许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licNo">
                  <a-input v-model="model.licNo" placeholder="请输入大件运输许可证号"  ></a-input>
                </a-form-model-item>
              </a-col>
               <a-col :span="12">
                 <a-form-model-item label="核查人员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recorder">
                  <a-input v-model="model.recorder" placeholder="请输入核查人员"  ></a-input>
                </a-form-model-item>
               </a-col>
               <a-col :span="12">
                   <a-form-model-item label="审核人员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reviewe">
                  <a-input v-model="model.reviewer" placeholder="请输入审核人员"  ></a-input>
                </a-form-model-item>
               </a-col>
              <a-col :span="12">
                <a-form-model-item label="是否车证一致" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isCertified">
                  <j-dict-select-tag type="radioButton" v-model="model.isCertified" dictCode="is_certified" placeholder="请选择是否车证一致" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-show="model.isCertified == 2">
                <a-form-model-item label="车证不符类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="uncertifiedType">
                  <j-dict-select-tag type="list" v-model="model.uncertifiedType" dictCode="un_certified_type" placeholder="请选择车证不符类型" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item  :wrapperCol="{ span: 23, offset:1}" prop="detailDesc">
                  <a-textarea v-model="model.detailDesc" placeholder="请输入具体描述" :auto-size="{ minRows: 3, maxRows: 8 }"/>
                </a-form-model-item>
              </a-col>
               <a-col :span="12">
                <a-form-model-item label="核查结果文件：" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="fileList">
                 <j-upload v-model="model.fileList" text="上传"></j-upload>
                </a-form-model-item>
              </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import JThirdAppDropdown from '@/components/jeecgbiz/thirdApp/JThirdAppDropdown'
  import mixedKeyboard from 'vehicle-keyboard/src/components/mixed-keyboard'
  export default {
    name: 'CheckSiteDataOverloadBigForm',
    components: {
        JThirdAppDropdown,
        mixedKeyboard
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        isDisabled:true,
        isshow:false,
        formData:{},
        model:{
          isCertified: 2
        },
        bigModel:{},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        args: {
          presetNumber: '', //预设车牌号码
          keyboardType: 0, //键盘类型[0,2]
          provinceName: '晋', //省份
          autoComplete: false
        },
        callbacks: {
          //// 回调函数
            onchanged: (presetNumber, isCompleted) => {
              // this.args.presetNumber = presetNumber;
              if(isCompleted)
              {
                  this.model.vehicleNo = presetNumber;
                  this.getBlur();
              }
              console.log(
                '当前车牌[变更]：' + presetNumber + ', 已完成:' + isCompleted
              );
            },
            onkeypressed: key => {
              console.log('当前按键：' + key.text);
            },
            oncompleted: (presetNumber, isAutoCompleted) => {
              this.numberArray = presetNumber.split('');
              this.model.vehicleNo = presetNumber;
              this.getBlur();
              console.log(
                '当前车牌[完成]：' + presetNumber + ', 自动完成:' + isAutoCompleted
              );
            },
            onmessage: function(message) {
              //console.info(message);
            }
        },
        url: {
          add: "/abnormal/bigIdCars/add",
          edit: "/abnormal/bigIdCars/edit",
          queryById: "/abnormal/bigIdCars/queryById",
          addBig: "/abnormal/bigIdCars/addBig"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
      this.clickOther();
    },
    methods: {
      add (record) {
        this.edit(record);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.args.presetNumber = this.model.vehicleNo;
        this.$nextTick(()=>{
          this.$refs.keyboard.init();
        });
        this.visible = true;
      },
      //选择框选择-是否证件合一
      onChangeCheckBox(checkedValues){
          this.model.isCertified = checkedValues;
          if(this.model.isCertified)
          {
              this.$set(this.model,'uncertifiedType',null);
          }
      },
       //获得焦点显示键盘
      getFocus(){
        this.isshow = true;
      },
      // 隐藏键盘
      getBlur(){
        this.isshow = false;
      },
            //点击其他区划，键盘消失
      clickOther(){
         let _this = this;
         document.addEventListener('click',function(e){
         var inputFrom =  document.getElementById("bigPlateInput");
         if(inputFrom){
          if(!inputFrom.contains(e.target)){
            _this.isshow = false;
          }
         }
         
      });
      },
      parseForm(){
          this.bigModel.checkDataId = this.model.id;
          this.bigModel.siteCode = this.model.siteCode;
          this.bigModel.vehicleNo = this.model.vehicleNo;
          this.bigModel.plateColor = this.model.plateColor;
          this.bigModel.total = this.model.total;
          this.bigModel.axles = this.model.axles;
          this.bigModel.licNo = this.model.licNo;
          this.bigModel.recorder = this.model.recorder;
          this.bigModel.reviewer = this.model.reviewer;
          this.bigModel.isCertified = this.model.isCertified;
          this.bigModel.detailDesc = this.model.detailDesc;
          this.bigModel.fileList = this.model.fileList;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        that.$refs.formBigCar.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            this.parseForm();
            httpAction(this.url.addBig,this.bigModel,'post').then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>