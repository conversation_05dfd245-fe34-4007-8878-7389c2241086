<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="企业编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入企业编码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseName">
              <a-input v-model="model.enterpriseName" placeholder="请输入企业名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="企业简称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseName">
              <a-input v-model="model.shortName" placeholder="请输入企业简称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="信用编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sourceCode">
              <a-input v-model="model.sourceCode" placeholder="请输入信用编码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="行政编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distCode">
              <j-area-linkage type="cascader" v-model="model.distCode" placeholder="请输入省市区" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="企业类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseType">
              <a-input v-model="model.enterpriseType" placeholder="请输入企业类型"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="企业类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <j-dict-select-tag v-model="model.type" dictCode="enterprise_type"
                placeholder="请选择企业类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteZt">
              <j-dict-select-tag  v-model="model.siteZt" dictCode="siteZt" placeholder="请选择站点状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="路线名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="roadName">
                <a-input v-model="model.roadName" placeholder="请输入路线名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="所属管理机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="chargeDepartment">
              <a-input v-model="model.chargeDepartment" placeholder="请输入所属管理机构"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="治超管理部门" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orgId">
              <a-input v-model="model.orgId" placeholder="请输入治超管理部门"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="单位地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-input v-model="model.address" placeholder="请输入单位地址"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="邮政编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="postcode">
              <a-input v-model="model.postCode" placeholder="请输入邮政编码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="负责人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="personInCharge">
              <a-input v-model="model.personInCharge" placeholder="请输入负责人"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="联系人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="contactor">
              <a-input v-model="model.contactor" placeholder="请输入联系人"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="telephone">
              <a-input v-model="model.telephone" placeholder="请输入联系电话"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="传真电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="chuanZhen">
              <a-input v-model="model.chuanZhen" placeholder="请输入传真电话"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="称重设备" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasWeighing">
              <j-dict-select-tag type="radioButton" v-model="model.hasWeighing" dictCode="has_equ"
                placeholder="请选择是否安装称重设备" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车牌抓拍设备" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasCamera">
              <j-dict-select-tag type="radioButton" v-model="model.hasCamera" dictCode="has_equ"
                placeholder="请选择是否安装车牌抓拍设备" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否联网" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasNetwork">
              <j-dict-select-tag type="radioButton" v-model="model.hasNetwork" dictCode="has_equ"
                placeholder="请选择是否联网" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否电子运单" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasYundan">
              <j-dict-select-tag type="radioButton" v-model="model.hasYundan" dictCode="has_yundan"
                placeholder="请选择是否采用电子运单" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="主要货物类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hl">
              <a-input v-model="model.hl" placeholder="请输入主要出/入厂货物类型"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="经度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="longitude">
              <a-input-number v-model="model.longitude" placeholder="请输入经度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="纬度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="latitude">
              <a-input-number v-model="model.latitude" placeholder="请输入纬度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="部级系统编码" :labelCol="labelCol" :wrapperCol="wrapperCol"
              prop="ministerialOerloadCode">
              <a-input v-model="model.ministerialOerloadCode" placeholder="请输入部级系统编码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="列为重点企业" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isFocus">
              <j-dict-select-tag type="list" v-model="model.isFocus" dictCode="is_focus" placeholder="请选择列为重点企业"
                @change="getIsFocus" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8" v-if="isShowFocusDate">
            <a-form-model-item label="列为时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="focusDate">
              <j-date placeholder="请选择列为重点企业时间" v-model="model.focusDate" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="所属部门" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sysOrgCode">
              <j-select-depart v-model="model.sysOrgCode" :trigger-change="true" customReturnField="orgCode"
                :multi="true" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'BaseEnterpriseForm',
  components: {},
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      model: {
        hasWeighing: 1,
        hasCamera: 1,
        hasNetwork: 1,
        hasYundan: 1,
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      isShowFocusDate: true,
      validatorRules: {
        code: [
          { required: true, message: '请输入企业编码!' },
          {
            validator: (rule, value, callback) =>
              validateDuplicateValue('base_enterprise', 'code', value, this.model.id, callback),
          },
        ],
        enterpriseName: [{ required: true, message: '请输入企业名称!' }],
        sourceCode: [{ required: false }, { pattern: /^.{6,18}$/, message: '请输入6到18位任意字符!' }],
        distCode: [{ required: true, message: '请输入行政编码!' }],
        telephone: [{ required: false }, { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!' }],
      },
      url: {
        add: '/system/baseEnterprise/add',
        edit: '/system/baseEnterprise/edit',
        queryById: '/system/baseEnterprise/queryById',
      },
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    this.getIsFocus(this.modelDefault.isFocus)
  },
  methods: {
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    getIsFocus(is_focus) {
      if (is_focus == '0') {
        //重点源头企业
        this.isShowFocusDate = true
      } else {
        this.isShowFocusDate = false
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
  },
}
</script>