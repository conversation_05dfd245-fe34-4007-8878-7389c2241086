<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入站点编码"  disabled=""></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车道号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lane">
              <a-input-number v-model="model.lane" placeholder="请输入车道号" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车道方向" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="direction">
              <a-input v-model="model.direction" placeholder="请输入车道方向"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="磅型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="poundType">
              <j-dict-select-tag type="list" v-model="model.poundType" dictCode="pound_type" placeholder="请选择磅型" @change="getType" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="超载率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overRate">
              <a-input-number v-model="model.overRate" placeholder="请输入超载率" style="width: 100%" :disabled="overRateDisabled"  />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-model-item label="检测规则" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkRule">
              <a-input v-model="model.checkRule" placeholder="请输入检测规则"  ></a-input>
            </a-form-model-item>
          </a-col> -->
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseUnattendedPointForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        overRateDisabled:false,
        overRate:"",
        confirmLoading: false,
        validatorRules: {
           code: [
              { required: true, message: '请输入站点编码!'},
           ],
           lane: [
              { required: true, message: '请输入车道号!'},
           ],
           direction: [
              { required: true, message: '请输入车道方向!'},
           ],
           poundType: [
              { required: true, message: '请输入磅型!'},
           ],
           overRate: [
              { required: true, message: '请输入超载率!'},
           ],
        },
        url: {
          add: "/system/baseUnattendedPoint/add",
          edit: "/system/baseUnattendedPoint/edit",
          queryById: "/system/baseUnattendedPoint/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add (siteCode) {
        console.log("无人检测点"+siteCode);
        this.model.code = siteCode;
        this.edit(this.model);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      getType(type){
          if(type == "1")
          {
            console.log(this.overRate);
            if(this.overRate == "") return;
            //选择动态磅
            this.model.overRate = this.overRate;
             this.overRateDisabled = false;
          }else{
            //选择静态磅
            this.overRate = this.model.overRate;
            this.model.overRate = 0;
            this.overRateDisabled = true;
          }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>