<template>
  <!-- 抽屉组件 -->
  <div class="custom-drawer" :class="{ 'drawer-open': open }">
    <div class="drawer-mask" v-if="open" @click="onClose"></div>

    <div class="drawer-content">
      <!-- 标题栏 -->
      <div class="drawer-header">
        <h3>{{ title }}</h3>
        <button class="close-btn" @click="onClose">×</button>
      </div>

      <!-- 内容区域 -->
      <div class="drawer-body">
        <!-- 图片预览 -->
        <div v-if="imgList.length" class="preview-section">
          <image-preview :image-list="imgList" />
          <hr class="divider" />
        </div>

        <!-- 详细信息 -->
        <div class="detail-section" v-if="detailSchema.length">
          <div>基础信息</div>
          <table class="key-value-table">
            <tbody>
              <tr v-for="item in detailSchema" :key="item.key">
                <th class="key-cell">{{ item.text }}:</th>
                <td class="value-cell">{{ detailData[item.value] || '-' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- 数据表格 -->
        <table v-if="columns.length" class="data-table">
          <thead v-if="head">
            <tr>
              <th colspan="100%">{{ head }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in dataSource" :key="index">
              <td v-for="col in columns" :key="col.key">{{ row[col.key] }}</td>
            </tr>
          </tbody>
        </table>

        <!-- 地图组件 -->
        <NewMap v-if="this.visible" :longitude="detailData.longitude || 0" :latitude="detailData.latitude || 0" />
      </div>
    </div>
  </div>
</template>

<script>
import NewMap from '@/components/NewMap'
  export default {
  name: 'NativeDrawer',
  props: {
    head: String,
  },
  components: {
    NewMap,
  },
  data() {
    return {
      open: false,
      title: '',
      detailData: {},
      columns: [],
      dataSource: [],
      imgList: [],
      detailSchema: [],
      api: '',
      visible: false,
    }
  },
  created() {
    
  },
  methods: {
    showMap() {
      console.log(this.detailData.longitude , this.detailData.latitude)
      if (this.detailData.longitude && this.detailData.latitude) this.visible = true
      return this.visible
    },
    openDrawer(record, config = {}) {
      this.open = true
      Object.assign(this, {
        detailData: { ...record },
        title: config.titleValue || '',
        detailSchema: config.detailSchemaValue || [],
        columns: config.columnValue || [],
        api: config.tableApi || '',
        imgList: config.imgListValue || [],
      })
      // this.fetchTableData()
      this.showMap()
    },

    async fetchTableData() {
      if (!this.api) return
      try {
        const res = await this.api()
        this.dataSource = res.records || []
      } catch (error) {
        console.error('数据获取失败:', error)
      }
    },

    onClose() {
      this.visible = false
      this.open = false
      this.resetState()
    },

    resetState() {
      this.imgList = []
      this.dataSource = []
      this.detailData = {}
      this.columns = []
      this.api = ''
    },
  },
}
</script>

<style scoped>
/* 抽屉容器 */
.custom-drawer {
  position: fixed;
  top: 0;
  right: -100%;
  width: 55%;
  height: 100%;
  transition: right 0.3s;
  z-index: 1000;
}

.drawer-open {
  right: 0;
}

/* 遮罩层 */
.drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

/* 内容区域 */
.drawer-content {
  position: relative;
  height: 100%;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
}

/* 头部样式 */
.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
}
/* 键值对表格样式 */
.key-value-table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.key-value-table th,
.key-value-table td {
  padding: 12px;
  border-bottom: 1px solid #e8e8e8;
  text-align: left;
}

.key-cell {
  width: 35%;
  font-weight: 500;
  color: #666;
  background-color: #fafafa;
}

.value-cell {
  color: #333;
  word-break: break-word;
}

.close-btn {
  font-size: 24px;
  cursor: pointer;
  background: none;
  border: none;
}

/* 内容主体 */
.drawer-body {
  padding: 24px;
  height: calc(100% - 64px);
  overflow-y: auto;
}

/* 表格样式 */
.data-table {
  width: 100%;
  margin-top: 20px;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  border: 1px solid #e8e8e8;
  text-align: left;
}

.data-table thead {
  background: #fafafa;
}

/* 分割线 */
.divider {
  margin: 20px 0;
  border-color: #575757;
}
</style>
