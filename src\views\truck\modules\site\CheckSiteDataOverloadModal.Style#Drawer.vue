<template>
  <a-drawer
    :title="title"
    :width="width"
    placement="right"
    :closable="false"
    @close="close"
    destroyOnClose
    :visible="visible">
    <check-site-data-overload-detail ref="detailForm" v-if="detailVisible" @ok="submitCallback"></check-site-data-overload-detail>
    <check-site-data-overload-add-form ref="addForm" v-if="addVisible" @ok="submitCallback"></check-site-data-overload-add-form>
    <check-site-data-overload-big-form ref="bigForm" v-if="bigVisible" @ok="submitCallback"></check-site-data-overload-big-form>
    <check-site-data-overload-special-form ref="specialForm" v-if="specialVisible" @ok="submitCallback"></check-site-data-overload-special-form>
    <check-site-data-overload-abnormal-form ref="abnormalForm" v-if="abnormalVisible" @ok="submitCallback"></check-site-data-overload-abnormal-form>
    <div class="drawer-footer">
      <a-button @click="handleCancel" style="margin-bottom: 0;">关闭</a-button>
      <a-button v-if="!disableSubmit"  @click="handleOk" type="primary" style="margin-bottom: 0;">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>

  import CheckSiteDataOverloadDetail from './CheckSiteDataOverloadDetail'
  import CheckSiteDataOverloadAddForm from './CheckSiteDataOverloadAddForm'
  import CheckSiteDataOverloadBigForm from './CheckSiteDataOverloadBigForm'
  import CheckSiteDataOverloadSpecialForm from './CheckSiteDataOverloadSpecialForm'
  import CheckSiteDataOverloadAbnormalForm from './CheckSiteDataOverloadAbnormalForm'

  export default {
    name: 'CheckSiteDataOverloadDrawerModal',
    components: {
      CheckSiteDataOverloadDetail,
      CheckSiteDataOverloadAddForm,
      CheckSiteDataOverloadBigForm,
      CheckSiteDataOverloadSpecialForm,
      CheckSiteDataOverloadAbnormalForm
    },
    data () {
      return {
        title:"操作",
        width:1000,
        visible: false,
        disableSubmit: false,
        detailVisible:false,
        addVisible:false,
        bigVisible:false,
        specialVisible:false,
        abnormalVisible:false
      }
    },
    methods: {
      setVisible(){
        this.visible=true
        this.addVisible= false
        this.detailVisible = false
        this.specialVisible = false
        this.abnormalVisible = false
        this.bigVisible=false
      },
      add (record) {
        this.setVisible();
        this.addVisible= true 
        this.$nextTick(()=>{
          this.$refs.addForm.add(record);
        })
      },
      bigAdd (record) {
        this.setVisible();
        this.bigVisible = true
        this.$nextTick(()=>{
          this.$refs.bigForm.add(record);
        });
      },
      specialAdd(record){
        this.setVisible();
        this.specialVisible = true
        this.$nextTick(()=>{
          this.$refs.specialForm.add(record);
        });
      },
       abnormalAdd(record){
        this.setVisible();
        this.abnormalVisible = true
        this.$nextTick(()=>{
          this.$refs.abnormalForm.add(record);
        });
      },
      show (record) {
        this.setVisible();
        this.detailVisible = true;
        this.$nextTick(()=>{
          this.$refs.detailForm.show(record);
        });
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleOk () {
        if(this.addVisible){
          this.$refs.addForm.submitForm();
        }
        if(this.bigVisible){
          this.$refs.bigForm.submitForm();
        }
        if(this.specialVisible){
          this.$refs.specialForm.submitForm();
        }
        if(this.abnormalVisible){
          this.$refs.abnormalForm.submitForm();
        }
       
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>

<style lang="less" scoped>
/** Button按钮间距 */
  .ant-btn {
    margin-left: 30px;
    margin-bottom: 30px;
    float: right;
  }
  .drawer-footer{
    position: absolute;
    bottom: -8px;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }
</style>