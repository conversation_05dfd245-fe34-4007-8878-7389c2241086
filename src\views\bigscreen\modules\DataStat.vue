<template>
  <div class="container">
    <dv-border-box-7 class="container_box">
      <div class="header">
        <div class="tabs">
          <button id="fixed" class="tab active" @click="(event) => openTab(event, 'fixed')">超限站</button>
          <button id="county" class="tab" @click="(event) => openTab(event, 'county')">源头企业</button>
        </div>

        <div class="chose_time">
          <a-space direction="vertical" :size="12"> </a-space>
          <div class="button_group">
            <a-select v-model="type" @change="handleChange" style="width: 66px">
              <a-select-option value="day">当天</a-select-option>
              <a-select-option value="month">当月</a-select-option>
              <a-select-option value="year">当年</a-select-option>
            </a-select>
          </div>
          <div class="button_select">
            <template v-if="type === 'day'">
              <a-date-picker
                :format="dateFormat"
                style="width: 116px; border-radius: 8px"
                v-model="date"
                @change="timeChange"
                allowClear
              />
            </template>
            <template v-else-if="type == 'month'">
              <a-month-picker
                style="width: 116px; border-radius: 8px"
                placeholder="选择月份"
                v-model="date"
                @change="timeChange"
                allowClear
              />
            </template>
            <template v-else-if="type == 'year'">
              <a-date-picker
                placeholder="选择年份"
                v-decorator="['year']"
                style="width: 116px; border-radius: 8px"
                mode="year"
                format="YYYY"
                :open="open"
                valueFormat="YYYY"
                v-model="date"
                allowClear
                @change="timeChange"
                @openChange="openChangeOne"
                @panelChange="panelChangeOne"
              />
            </template>
          </div>
        </div>
      </div>
      <div class="content" v-if="activeTab === 'fixed'" id="fixed2">
        <div class="left">
          <div class="data" style="padding-bottom: 8px">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ dy_val }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">辆</div>
            </div>
            <div class="info title">动态磅数据</div>
          </div>
          <div class="data">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ static_val }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">辆</div>
            </div>
            <div class="info title">静态磅数据</div>
          </div>
        </div>
        <div class="right" style="display: flex; flex-direction: column; justify-content: space-between">
          <div class="data" style="padding-bottom: 8px">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ copy_val }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">辆</div>
            </div>
            <div class="info title">复检数据</div>
          </div>
          <div class="data">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ over_rate }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">%</div>
            </div>
            <div class="info title">超载率</div>
          </div>
        </div>
      </div>

      <div class="content" v-if="activeTab === 'county'" id="county2">
        <div class="left">
          <div class="data" style="padding-bottom: 8px">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ inNum + outNum }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">辆</div>
            </div>
            <div class="info title">出入厂数</div>
          </div>
          <div class="data">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ checkNum }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">辆</div>
            </div>
            <div class="info title">过车数</div>
          </div>
        </div>
        <div class="right" style="display: flex; flex-direction: column; justify-content: space-between">
          <div class="data" style="padding-bottom: 8px">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ overNum }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">辆</div>
            </div>
            <div class="info title">超载数量</div>
          </div>
          <div class="data">
            <div class="info num">
              <span style="font-size: 36px; color: #4898f1">{{ overRate }}</span>
              <div class="info" style="display: inline-block; vertical-align: top; padding-top: 33px">%</div>
            </div>
            <div class="info title">超载率</div>
          </div>
        </div>
      </div>
    </dv-border-box-7>
  </div>
</template>

<script>
import CountFlop from '@/views/bigscreen/components/CountFlop/index'
import ProcessRound from '@/views/bigscreen/components/ProcessRound/index'
import { getAction, deleteAction } from '@/api/manage'
import moment from 'moment'
export default {
  name: 'DataStat',
  components: {
    moment,
    CountFlop,
    ProcessRound,
  },
  data() {
    return {
      title: '超限率',
      open: false,
      type: 'day',
      dateFormat: 'YYYY-MM-DD',
      monthFormat: 'YYYY-MM',
      date: moment(),
      url: {
        total: 'bigScreen/weightTotal',
      },
      dy_val: 0,
      static_val: 0,
      copy_val: 0,
      over_rate: 0,
      activeTab: 'fixed',
      copyDataCount: 0,
      dynamicDataCount: 0,
      staticDataCount: 0,
      overRate: 0,
      checkNum: 0,
    }
  },
  mounted() {
    this.init()
    this.initData(this.date)
    this.getData(this.date)
  },
  methods: {
    moment,
    init() {
      this.config = {
        number: [100],
        style: {
          fontSize: 16,
          fill: '#3de7c9',
        },
      }
    },
    handleChange(value) {
      this.type = value
      this.initData(this.date)
      this.getData(this.date)
    },
    openTab(evt, tabName) {
      this.activeTab = tabName
      // 隐藏所有内容
      document.querySelectorAll('.tab').forEach((content) => {
        content.classList.remove('active')
      })

      // 取消所有tab激活状态
      document.querySelectorAll('.tab').forEach((tab) => {
        tab.classList.remove('active')
      })

      // 显示选中内容
      document.getElementById(tabName).classList.add('active')
      evt.currentTarget.classList.add('active')
      this.initData(this.date)
      this.getData(this.date)
    },
    timeChange(val) {
      this.initData(val)
      this.getData(val)
    },
    initData(time) {
      var datetime = moment(time).format('YYYY-MM-DD')
      getAction(this.url.total, { type: this.type, time: datetime }).then((res) => {
        this.dy_val = res.result.dynamicDataCount
        this.static_val = res.result.staticDataCount
        this.copy_val = res.result.copyDataCount
        this.over_rate = parseInt(res.result.overRate)
      })
    },
    getData(time) {
      var datetime = moment(time).format('YYYY-MM-DD')
      getAction('/bigScreen/enterpriseWeightTotal', { type: this.type, time: datetime }).then((res) => {
        console.log(res, '===>resres')
        this.inNum = Number(res.result.inNum)
        this.outNum = Number(res.result.outNum) 
        this.overNum = res.result.overNum
        this.checkNum = res.result.checkNum
        this.overRate = parseInt(res.result.overRate)
      })
    },

    // 弹出日历和关闭日历的回调
    openChangeOne(status) {
      if (status) {
        this.open = true
      }
    },
    // 得到年份选择器的值
    panelChangeOne(value) {
      this.open = false
      this.date = value
      this.initData(value)
      this.getData(value)
    },
  },
}
</script>
<style lang="less" scoped>
.container {
  position: relative;

  // background: red;
  .container_box {
    .header {
      display: flex;
      flex: 1;
      flex-direction: row;
      justify-content: space-between;
      margin: 10px 10px 5px 15px;

      .title {
        font-size: 0.8125rem /* 13/16 */;
        flex: 1;
        font-weight: bolder;
      }

      .chose_time {
        width: 120px;
        display: flex;
        flex: 1;
        flex-direction: row;
        justify-content: end;

        .button_group {
          padding: 0px 10px;
        }

        .button_select {
          // padding: 0px 10px;
        }
      }
    }

    .content {
      height: 9rem /* 145/16 */;
      display: flex;
      flex-direction: row;
      .left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        text-align: center;
        .data {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .info {
            margin-left: 5px;
            flex: 1;
          }

          .title {
            font-size: 0.9rem;
            flex: 1;
          }

          .num {
            flex: 1;
          }
        }
      }

      .right {
        text-align: center;
        flex: 1;
      }
    }
  }
}

.myself {
  height: 32px;

  /deep/ .count-flop {
    height: 2rem /* 32/16 */;
    line-height: 32px;
    font-size: 36px;
    color: red;
  }

  /deep/ .count-flop-box {
    margin-right: 0;
    width: 22px;
    border: 0;
    border-radius: 0;
    line-height: 32px;
    margin-bottom: 3px;
  }

  /deep/ .count-flop-point {
    margin-right: 0;
  }

  /deep/ .count-flop-unit {
    font-size: 1.5625rem /* 25/16 */;
  }
}

/deep/ .ant-select {
}

/deep/ .ant-select-selection {
  border: none;
  border-radius: 0px;
  background: #1769ff;
  color: white;
}

/deep/ .ant-select-selection--single {
  height: 20px;
}

/deep/ .ant-select-selection__rendered {
  margin-left: 5px;
  line-height: 20px;
}

/deep/ .ant-select-selection-selected-value {
  -webkit-transform: scale(0.8);
}

/deep/ .ant-select-arrow {
  color: white;
}

/deep/ .ant-select-dropdown-menu-item {
  font-size: 12px;
  line-height: 16px;
}

/deep/ .ant-calendar-picker {
  font-size: 12px;
  color: white;
}

/deep/ .ant-input {
  background-color: #1769ff;
  border: 0px solid #d9d9d9;
  border-radius: 0px;
  height: 20px;
  color: #fff;

  &::-webkit-input-placeholder {
    color: white;
    font-size: 12px;
  }
}
.tab-container {
  margin: 20px;
}

.tabs {
  border-bottom: 1px solid #eee;
}

.tab {
  background: none;
  border: none;
  padding: 0px 15px 5px 3px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.tab:hover {
  background-color: #f5f5f5;
}

.tab-content {
  display: none;
  padding: 20px;
  border: 1px solid #eee;
  border-top: none;
}

.tab-content.active {
  display: block;
}
/deep/ .ant-input:hover {
  border: none;
}

/deep/ .ant-calendar-picker-icon {
  color: white;
}

/deep/ .anticon anticon-close-circle ant-calendar-picker-clear {
}

/* 新增标签页样式 */
.tabs {
  border-bottom: 2px solid rgba(61, 231, 201, 0.3); /* 使用系统主题色 */
  margin-left: 15px;
}

.tab {
  background: none;
  border: none;
  padding: 6px 24px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s;
  position: relative;

  /* 添加科技感渐变边框 */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #3de7c9, transparent);
    transition: all 0.3s;
  }

  &.active {
    color: #3de7c9;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(61, 231, 201, 0.5);

    &::after {
      width: 100%;
      left: 0;
      background: #3de7c9;
    }
  }

  &:hover {
    color: #3de7c9;
    background: rgba(61, 231, 201, 0.1);
  }
}

/* 时间选择器样式优化 */
.chose_time {
  .button_group {
    /deep/ .ant-select-selection {
      background: linear-gradient(145deg, #182b4d 0%, #1a3658 100%);
      border: 1px solid rgba(61, 231, 201, 0.3); /* 复用图表中的蓝绿色 */
      box-shadow: inset 0 2px 4px rgba(61, 231, 201, 0.1);
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }

  .button_select {
    /deep/ .ant-calendar-picker {
      input {
        background: linear-gradient(145deg, #182b4d 0%, #1a3658 100%);
        border: 1px solid rgba(61, 231, 201, 0.3); /* 复用图表中的蓝绿色 */
        box-shadow: inset 0 2px 4px rgba(61, 231, 201, 0.1);
        border-radius: 8px;
        &::placeholder {
          color: rgba(255, 255, 255, 0.8);
        }
      }
      .ant-calendar-picker-icon {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

/* 组件布局优化 */
.header {
  margin: 20px 15px 10px;
  align-items: center;

  .tabs {
    flex: 1;
  }

  .chose_time {
    width: auto;
    gap: 10px;

    .button_group,
    .button_select {
      /deep/ .ant-select,
      /deep/ .ant-calendar-picker {
        width: 120px;
      }
    }
  }
}
</style>
