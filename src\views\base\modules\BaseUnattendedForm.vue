<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入站点编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="站点名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入站点名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="站点简称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="uname">
              <a-input v-model="model.uname" placeholder="请输入站点简称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="检测车道数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="jccds">
              <a-input-number v-model="model.jccds" placeholder="请输入检测车道数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distCode">
             <j-area-linkage type="cascader" v-model="model.distCode" placeholder="请输入省市区"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="站点状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteZt">
              <j-dict-select-tag type="list" v-model="model.siteZt" dictCode="siteZt" placeholder="请选择站点状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="IP地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ip">
              <a-input v-model="model.ip" placeholder="请输入IP地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="经度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="longitude">
              <a-input v-model="model.longitude" placeholder="请输入经度"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="纬度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="latitude">
              <a-input v-model="model.latitude" placeholder="请输入纬度"  ></a-input>
            </a-form-model-item>
          </a-col>
          
          <a-col :span="24">
            <a-form-model-item label="所属部门" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sysOrgCode">
              <j-select-depart v-model="model.sysOrgCode" :trigger-change="true" customReturnField="orgCode" :multi="true"  />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseUnattendedForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        overRateDisabled:false,
        overRate:"",
        confirmLoading: false,
        validatorRules: {
           code: [
              { required: true, message: '请输入站点编码!'},
              { pattern: '[\w.%+-]', message: '不符合校验规则!'},
              { validator: (rule, value, callback) => validateDuplicateValue('base_unattended', 'code', value, this.model.id, callback)},
           ],
           name: [
              { required: true, message: '请输入站点名称!'},
           ],
           uname: [
              { required: true, message: '请输入站点简称!'},
           ],
           jccds: [
              { required: true, message: '请输入检测车道数!'},
           ],
           distCode: [
              { required: true, message: '请输入行政区划!'},
           ],
           siteZt: [
              { required: true, message: '请输入站点状态!'},
           ],
        },
        url: {
          add: "/system/baseUnattended/add",
          edit: "/system/baseUnattended/edit",
          queryById: "/system/baseUnattended/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
       getType(type){
          if(type == "1")
          {
            //选择动态磅
            this.model.overRate = this.overRate;
          }else{
            //选择静态磅
            this.overRate = this.model.overRate;
            this.model.overRate = 0;
            this.overRateDisabled = true;
          }
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>