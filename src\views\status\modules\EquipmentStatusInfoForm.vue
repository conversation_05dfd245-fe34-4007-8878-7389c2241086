<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="数据唯一标识" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="uniqueId">
              <a-input v-model="model.uniqueId" placeholder="请输入数据唯一标识"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="站点编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <j-dict-select-tag type="list" v-model="model.siteCode" dictCode="base_site,name,code" placeholder="请选择站点" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipCode">
              <a-input v-model="model.equipCode" placeholder="请输入设备编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="设备类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipType">
              <j-dict-select-tag type="list" v-model="model.equipType" dictCode="equip_type" placeholder="请选择设备类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="设备状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipStatus">
              <j-multi-select-tag type="list_multi" v-model="model.equipStatus" dictCode="equip_status" placeholder="请选择设备状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="消息发送时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sendtime">
              <a-input v-model="model.sendtime" placeholder="请输入消息发送时间"  ></a-input>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-model-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="createTime">
              <j-date placeholder="请选择创建时间"  v-model="model.createTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col> -->
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'EquipmentStatusInfoForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           siteCode: [
              { required: true, message: '请输入站点编号!'},
           ],
           equipCode: [
              { required: true, message: '请输入设备编号!'},
           ],
           equipType: [
              { required: true, message: '请输入设备类型!'},
           ],
           equipStatus: [
              { required: true, message: '请输入设备状态!'},
           ],
           sendtime: [
              { required: true, message: '请输入消息发送时间!'},
           ],
        },
        url: {
          add: "/status/equipmentStatusInfo/add",
          edit: "/status/equipmentStatusInfo/edit",
          queryById: "/status/equipmentStatusInfo/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>