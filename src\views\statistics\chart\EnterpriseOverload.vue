<template>
  <div>
    <a-card :bordered="false">
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="县区">
                <j-area-linkage type="cascader" v-model="queryParam.distCode" placeholder="请选择省市区"
                  @change="distChange" />
              </a-form-item>
            </a-col>
            <a-col :xl="4" :lg="6" :md="8" :sm="24">
              <a-form-item label="企业">
                <j-dict-select-tag placeholder="请选择企业" v-model="queryParam.siteCode" :dictCode="dist"
                  @change="jdictChange" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="时间段">
                <j-month-range v-model="date" style="width:100%" @change="dateRangeChange"></j-month-range>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
                </a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <div class="no-print" style="text-align: right">
        <a-button v-print="'#printContent'" ghost type="primary">打印</a-button>
      </div>
      <a-row ref="print" id="printContent" class="abcdefg">
        <line-chart-multid title="超载数据分析" :height="height" :dataSource="countSource" :fields="fileds"  :aliases="aliases"/>
      </a-row>
    </a-card>
  </div>
</template>
<script>
import LineChartMultid from '@/components/chart/LineChartMultid'
import ACol from 'ant-design-vue/es/grid/Col'
import JYearPicker from '@/components/jeecg/JYearPicker'
import JMonthPicker from '@/components/jeecg/JMonthPicker'
import { getAction } from '@/api/manage'
import moment from 'dayjs'
import JDateRange from '@/components/jeecg/JDateRange'
import JMonthRange from '@/components/jeecg/JMonthRange'
export default {
  name: 'EnterpriseOverload',
  components: {
    ACol,
    LineChartMultid,
    JYearPicker,
    JMonthPicker,
    JDateRange,
    JMonthRange
  },
  data() {
    return {
      description: '企业超载统计表',
      queryParam: {
      },
      countSource: [],
      height: 500,
      dist: "base_enterprise,enterprise_name,code",
      date: [moment().startOf("year").format('YYYY-MM'), moment().endOf("year").format('YYYY-MM')],
      url: {
        overload: "/statistics/statisticsEnterprise/overload"
      },
      fileds:['overNum','num'],
      aliases:[{field:'overNum',alias:'超载'},{field:'num',alias:'过车数'}]
    }
  },
  created() {
    this.queryParam.startTime = this.date[0];
    this.queryParam.endTime = this.date[1];
    this.loadDate(this.url.overload);
  },
  methods: {
    searchQuery() {
      this.loadDate(this.url.overload);
    },
    searchReset() {
      console.log("点击了重置");
      this.queryParam = {}
      this.date = [moment().startOf("year").format('YYYY-MM'), moment().endOf("year").format('YYYY-MM')];
      this.queryParam.startTime = this.date[0];
      this.queryParam.endTime = this.date[1];
      this.dist = "base_enterprise,enterprise_name,code";
      this.loadDate(this.url.overload);

    },
    loadDate(url) {
      getAction(url, this.queryParam, "get").then((res) => {
        if (res.success) {
          this.countSource = [];
          this.convertDataSource(res.result);
        } else {
          var that = this;
          that.$message.warning(res.message);
        }
      });
    },
    convertDataSource(data) {
      for (let i = 0; i < data.length; i++) {
        this.countSource.push({
          type: data[i].month,
          overNum: data[i].overNum,
          num: data[i].num,
        })
      }

    },
    distChange(data) {
      this.dist = "base_enterprise,enterprise_name,code,dist_code=" + data;
      delete this.queryParam['site_code'];
    },
    jdictChange(data) {
      this.queryParam.siteCode = data;
      this.$forceUpdate()
    },
    dateRangeChange(dateStr) {
      this.date = dateStr;
      this.queryParam.startTime = dateStr[0].format('YYYY-MM');
      this.queryParam.endTime = dateStr[1].format('YYYY-MM');
    }

  }
}
</script>
<style scoped>
.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}

.statistic {
  padding: 0px !important;
  margin-top: 50px;
}

.statistic h4 {
  margin-bottom: 20px;
  text-align: center !important;
  font-size: 24px !important;
  ;
}

.statistic #canvas_1 {
  width: 100% !important;
}
</style>
