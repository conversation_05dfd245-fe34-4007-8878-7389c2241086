<template>
  <div id="customMap" style="width: 100%; height: 500px"></div>
</template>

<script>
import { load } from '@amap/amap-jsapi-loader';

export default {
  name: 'NewMap',
  props: {
    longitude: { type: Number, default: '' },
    latitude: { type: Number, default: '' }
  },
  data() {
    return {
      map: null,
      markers: [[parseFloat(this.longitude), parseFloat(this.latitude)]],
      center: [parseFloat(this.longitude), parseFloat(this.latitude)]
    };
  },
  mounted() {
    this.initMap();
  },
  methods: {
    async initMap() {
      try {
        await load({
          key: '3ba95f602bc77ee6d74a6e1a54b893d9',
          version: '1.4.15',
        });
        
        this.map = new window.AMap.Map('customMap', {
          center: this.center,
          zoom: 13,
          dragEnable: false,
          zoomEnable: false,
        });

        this.markers.forEach(position => {
          new window.AMap.Marker({
            position,
            map: this.map
          });
        });

      } catch (error) {
        console.error('高德地图加载失败:', error);
      }
    }
  }
};
</script>

<style scoped>
#customMap {
  width: 100%;
  height: 500px;
}

.title:hover {
  cursor: pointer;
  color: #1890ff;
  font-weight: 600;
}

#my-panel {
  width: 300px;
  height: 300px;
}
</style>