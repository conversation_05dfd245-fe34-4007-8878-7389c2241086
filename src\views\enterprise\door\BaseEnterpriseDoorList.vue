<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="出入口名称">
              <a-input placeholder="请输入出入口名称" v-model="queryParam.doorName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button type="primary" icon="download" @click="handleExportXls('企业出入口表')">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query
        :fieldList="superFieldList"
        ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
     
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            v-preview="getImgView(text)" 
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <template slot="plateSlot" slot-scope="text, record">
          <plate :plate="text" :color="Number(record.plateColor)"></plate>
        </template>
       

        <span slot="action" slot-scope="text, record">
          <!-- <a @click="handleEdit(record)">编辑</a> -->
          <a @click="handleDetail(record)">详情</a>
          <!-- <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown> -->
        </span>
      </a-table>
    </div>

    <base-enterprise-door-modal ref="modalForm" @ok="modalFormOk"></base-enterprise-door-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import BaseEnterpriseDoorModal from './modules/BaseEnterpriseDoorModal'
import Plate from '@/components/plate/Plate'
export default {
  name: 'BaseEnterpriseDoorList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    BaseEnterpriseDoorModal,
    Plate
  },
  data() {
    return {
      description: '企业出入口表管理页面',
      // 表头
      columns: [
        {
          title: '企业名称',
          align: 'center',
          dataIndex: 'enterpriseName',
        },
        {
          title: '车牌号',
          align: 'center',
          dataIndex: 'vehicleNo',
          width: 200,
          scopedSlots: { customRender: 'plateSlot' },
        },
        {
          title: '入厂时间',
          align: 'center',
          dataIndex: 'entryTime',
        },
        {
          title: '入厂图片',
          align: 'center',
          dataIndex: 'entryImage',
          // customRender: render.renderImage,
          // scopedSlots: { customRender: 'imgSlot' },
          customRender: (text) => {
            const dataSource = text || ''
            // 使用 Vue 的 createElement (h) 构建虚拟节点
            return this.$createElement('div', [
            dataSource == null || dataSource == '' 
                ? this.$createElement(
                    'span',
                    {
                      style: { fontSize: '12px', fontStyle: 'italic' },
                    },
                    '无图片'
                  )
                : this.$createElement('vue-preview', {
                    attrs: {
                      slides: [
                        {
                          w: 1200, //设置以大图浏览时的宽度
                          h: 800, //设置以大图浏览时的高度
                          src: this.getImgView(dataSource),
                          msrc: this.getImgView(dataSource),
                        },
                      ],
                    },
                    class: 'preview',
                  }),
            ])
          },
        },
        {
          title: '出厂时间',
          align: 'center',
          dataIndex: 'exitTime',
        },
        {
          title: '出厂图片',
          align: 'center',
          dataIndex: 'exitImage',
          customRender: (text) => {
            const dataSource = text || ''
            // 使用 Vue 的 createElement (h) 构建虚拟节点
            return this.$createElement('div', [
            dataSource == null || dataSource == '' 
                ? this.$createElement(
                    'span',
                    {
                      style: { fontSize: '12px', fontStyle: 'italic' },
                    },
                    '无图片'
                  )
                : this.$createElement('vue-preview', {
                    attrs: {
                      slides: [
                        {
                          w: 1200, //设置以大图浏览时的宽度
                          h: 800, //设置以大图浏览时的高度
                          src: this.getImgView(dataSource),
                          msrc: this.getImgView(dataSource),
                        },
                      ],
                    },
                    class: 'preview',
                  }),
            ])
          },
        },
      ],
      url: {
        list: '/enterprise/door/baseEnterpriseDoor/list',
        delete: '/enterprise/door/baseEnterpriseDoor/delete',
        deleteBatch: '/enterprise/door/baseEnterpriseDoor/deleteBatch',
        exportXlsUrl: '/enterprise/door/baseEnterpriseDoor/exportXls',
        importExcelUrl: '/enterprise/door/baseEnterpriseDoor/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'enterpriseCode', text: '企业编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'doorName', text: '出入口名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'doorCode', text: '出入口编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'doorStatus', text: '出入口状态', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style >
@import '~@assets/less/common.less';
.preview figure img {
	width: 75px;
	height: 45px;
}
</style>
