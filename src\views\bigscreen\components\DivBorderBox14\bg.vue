<template>
  <div class="gridView">
    <!--  左上角-->
    <div class="gLeftTop"></div>
    <!--  顶部中间-->
    <div class="gTopCenter"></div>
    <!--  右上角-->
    <div class="gRightTop"></div>
    <!--    中间部分-->
    <div class="gCenterMain">
      <!--  左侧中间-->
      <div class="gLeftCenter"></div>
      <!--  右侧中间-->
      <div class="gRightCenter"></div>
      <!--  中间-->
      <div class="gCenter"></div>
    </div>
    <!--  左下角-->
    <div class="gLeftBottom"></div>
    <!--  底部中间-->
    <div class="gBottomCenter"></div>
    <!--  右下角-->
    <div class="gRightBottom"></div>
  </div>
</template>

<script>

export default {
  name: "DivBorderBox14Bg"
}
</script>

<style lang="less" scoped>
.gridView {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-width: 20vh /* 220/16 */;
  bottom: 0;
  right: 0;
  z-index: -1;
  pointer-events: none; //避免div层被点击
  //左上角
  .gLeftTop {
    background: url(~@/assets/screen/divbox14/left_top.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    width: 3.125rem /* 50/16 */;
    height: 3.125rem /* 50/16 */;
  }

  //顶部中间
  .gTopCenter {
    width: calc(100% - 3.125rem /* 50/16 */ - 3.125rem /* 50/16 */);
    left: 3.125rem /* 50/16 */;
    position: absolute;
    height: 3.125rem /* 50/16 */;
    background: url(~@/assets/screen/divbox14/top.png) repeat-x;
    background-size: 100% 100%;
  }

  //右上角
  .gRightTop {
    background: url(~@/assets/screen/divbox14/right_top.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    top: 0;
    width: 3.125rem /* 50/16 */;
    height: 3.125rem /* 50/16 */;
  }

  //左下角
  .gLeftBottom {
    background: url(~@/assets/screen/divbox14/left_bottom.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50PX;
    height: 50PX;
  }

  //底部中间
  .gBottomCenter {
    width: calc(100% - 50PX - 50PX);
    left: 50PX;
    bottom: 0;
    position: absolute;
    height: 50PX;
    background: url(~@/assets/screen/divbox14/bottom.png) repeat-x;
    background-size: 100% 100%;
  }

  //右下角
  .gRightBottom {
    background: url(~@/assets/screen/divbox14/right_bottom.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    bottom: 0;
    width: 50PX;
    height: 50PX;
  }

  //中间部分
  .gCenterMain {
    width: 100%;
    height: calc(100% - 100PX);
    position: absolute;
    top: 50PX;
    bottom: 50PX;
    //左侧中间
    .gLeftCenter {
      left: 0;
      height: 100%;
      position: absolute;
      background: url(~@/assets/screen/divbox14/left_center.png);
      background-size: 50px 20px;
    }

    .gCenter {
      width: calc(100% - 50PX - 50PX);
      left: 50PX;
      bottom: 0;
      position: absolute;
      height: 100%;
      background: url(~@/assets/screen/divbox14/center.png);
      background-size: 100% 100%;
    }

    //右侧中间
    .gRightCenter {
      height: 100%;
      position: absolute;
      right: 0;
      background: url(~@/assets/screen/divbox14/right_center.png);
      background-size: 100% 100%;
      width: 50PX;
    }
  }
}
</style>
