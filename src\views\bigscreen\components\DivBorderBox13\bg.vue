<template>
  <div class="gridView">
    <!--  左上角-->
    <div class="gLeftTop"></div>
    <!--  顶部中间-->
    <div class="gTopCenter"></div>
    <!--  右上角-->
    <div class="gRightTop"></div>
    <!--    中间部分-->
    <div class="gCenterMain">
      <!--  左侧中间-->
      <div class="gLeftCenter"></div>
      <!--  右侧中间-->
      <div class="gRightCenter"></div>
      <!--  中间-->
      <div class="gCenter"></div>
    </div>
    <!--  左下角-->
    <div class="gLeftBottom"></div>
    <!--  底部中间-->
    <div class="gBottomCenter"></div>
    <!--  右下角-->
    <div class="gRightBottom"></div>
  </div>
</template>

<script>

export default {
  name: "gridView"
}
</script>

<style lang="less" scoped>
.gridView {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 80px;
  bottom: 0;
  right: 0;
  z-index: -1;
  pointer-events: none; //避免div层被点击
  //左上角
  .gLeftTop {
    background: url(~@/assets/screen/divbox13/left_top.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    width: 80PX;
    height: 60PX;
  }

  //顶部中间
  .gTopCenter {
    width: calc(100% - 80PX - 8PX);
    left: 80PX;
    position: absolute;
    height: 60PX;
    background: url(~@/assets/screen/divbox13/top.png) repeat-x;
    background-size: 100% 100%;
  }

  //右上角
  .gRightTop {
    background: url(~@/assets/screen/divbox13/right_top.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    top: 0;
    width: 8PX;
    height: 60PX;
  }

  //左下角
  .gLeftBottom {
    background: url(~@/assets/screen/divbox13/left_bottom.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 75PX;
    height: 60PX;
  }

  //底部中间
  .gBottomCenter {
    width: calc(100% - 75PX - 17PX);
    left: 75PX;
    bottom: 0;
    position: absolute;
    height: 60PX;
    background: url(~@/assets/screen/divbox13/bottom.png) repeat-x;
    background-size: 100% 100%;
  }

  //右下角
  .gRightBottom {
    background: url(~@/assets/screen/divbox13/right_bottom.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    bottom: 0;
    width: 17PX;
    height: 60PX;
  }

  //中间部分
  .gCenterMain {
    width: 100%;
    height: calc(100% - 60PX - 60PX);
    position: absolute;
    top: 60PX;
    bottom: 60PX;
    //左侧中间
    .gLeftCenter {
      left: 0;
      height: 100%;
      position: absolute;
      background: url(~@/assets/screen/divbox13/left_center.png) repeat-y;
      background-size: 100%;
      width: 40PX;
    }

    .gCenter {
      width: calc(100% - 40PX - 50PX);
      left: 40PX;
      bottom: 0;
      position: absolute;
      height: 100%;
      background: url(~@/assets/screen/divbox13/center.png);
      background-size: 100% 100%;
    }

    //右侧中间
    .gRightCenter {
      height: 100%;
      position: absolute;
      right: 0;
      background: url(~@/assets/screen/divbox13/right_center.png);
      background-size: 100% 100%;
      width: 50PX;
    }
  }
}
</style>