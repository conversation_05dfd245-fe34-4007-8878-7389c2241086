<template>
    <a-range-picker :ranges='timeRange' format="YYYY-MM-DD HH:mm:ss" :default-value="defalutValue" :placeholder="['开始时间', '结束时间']" @change="createChange" style="width: 100%" :disabled-date="disabledDate"
        :value="timeValue" allowClear />
</template>
<script>
import moment from 'moment'

export default {
    name: "JDateRange",
    components: {
        moment
    },
    props: {
        timeValue: {
            type: Array,
            required: false,
        },
        defalutValue:{
            type: Array,
            required: false,
        },
    },

    created() {
    },
    data() {
        return {
            timeRange:
            {
                今天: [moment().startOf('day'), moment()],
                昨天: [moment().startOf('day').subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
                最近三天: [moment().startOf('day').subtract(2, 'days'), moment().endOf('day')],
                最近一周: [moment().startOf('day').subtract(1, 'weeks'), moment()],
                本月: [moment().startOf('month'), moment()],
                本年: [moment().startOf('year'), moment()],
                去年: [moment().year(moment().year() - 1).startOf('year'), moment().year(moment().year() - 1).endOf('year') ]
            },

        }
    },

    methods: {
        moment,
        createChange(dates, dateStrings) {
            this.$emit('change', dates, dateStrings);
        },
        disabledDate(current) {
            return current && current > moment().subtract(0, 'days').endOf('day')
        }


    }
}
</script>
 
<style scoped></style>