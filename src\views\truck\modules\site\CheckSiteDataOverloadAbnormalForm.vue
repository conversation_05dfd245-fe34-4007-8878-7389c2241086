<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
             <a-form-model-item label="站点名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <j-dict-select-tag type="list" v-model="model.siteCode" dictCode="base_site,name,code" placeholder="请选择站点名称"  :disabled="isDisabled"/>
            </a-form-model-item>
          </a-col>
           <a-col :span="12">
             <div id="errorPlateInput">
                <a-form-model-item label="车牌号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNo" >
                  <a-input v-model="model.vehicleNo" placeholder="请输入车牌号码"  @focus="getFocus" :disabled="isModVehicleNoDisabled" ></a-input>
                  <transition name="info">
                      <div style="width: 350px;height: 175px;background: red;z-index: 20;position: absolute;left:-30px" v-show="isshow">
                          <div id="single-keyboard-box">
                              <mixed-keyboard ref="keyboard" :args="args"
                                :callbacks="callbacks">
                              </mixed-keyboard>
                          </div>
                      </div>
                  </transition>
                </a-form-model-item>
            </div>
           </a-col>
            <a-col :span="12">
              <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateColor">
                <j-dict-select-tag type="list" v-model="model.plateColor" dictCode="plate_color" placeholder="请选择车牌颜色" :disabled="isModPlateColorDisabled" />
              </a-form-model-item>
            </a-col>
             <a-col :span="12">
                <a-form-model-item label="总重(Kg)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="total">
                    <a-input-number v-model="model.total" placeholder="请输入总重" style="width: 100%"  addon-after="KG" :disabled="isModTotalDisabled" />
                </a-form-model-item>
             </a-col>
              <a-col :span="12">
                <a-form-model-item label="检测类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkType">
                  <j-dict-select-tag type="list" v-model="model.checkType" dictCode="check_type" placeholder="请选择检测类型" :disabled="isDisabled" />
                </a-form-model-item>
             </a-col>
              <a-col :span="12">
                <a-form-model-item label="轴型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleAxlesType">
                  <a-input v-model="model.vehicleAxlesType" placeholder="请输入车型代码"   :disabled="isDisabled"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
                  <j-dict-select-tag type="list" v-model="model.axles" dictCode="axles" placeholder="请选择轴数" @change="handleUpdate" :disabled="isModAxlesDisabled" />
                </a-form-model-item>
              </a-col>
               <a-col :span="12">
                <a-form-model-item label="限重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitWeight">
                  <a-input v-model="model.limitWeight" placeholder="请输入限重"  :disabled="isModLimitWeightDisabled"  addon-after="KG" ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="超载量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overWeight">
                  <a-input v-model="model.overWeight" placeholder="请输入超载量"  :disabled="isDisabled" addon-after="KG" ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="超载率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overRate">
                  <a-input v-model="model.overRate" placeholder="请输入超载率"  :disabled="isDisabled"  addon-after="%">
                  </a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="操作类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="operateType">
                     <j-dict-select-tag v-model="model.operateType" placeholder="请选择操作类型" dictCode="op_type"/>  
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="修改原因" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="updateReason">
                  <j-multi-select-tag type="list_multi" v-model="model.updateReason" @change="onChangeReason" dictCode="update_reason" placeholder="请选择修改原因" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item  :wrapperCol="{ span: 22, offset:1}" prop="updateDesc">
                  <a-textarea v-model="model.updateDesc" placeholder="请输入具体原因" :auto-size="{ minRows: 3, maxRows: 8 }"/>
                </a-form-model-item>
              </a-col>
          
        </a-row>
       
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction,postAction } from '@/api/manage'
  import JThirdAppDropdown from '@/components/jeecgbiz/thirdApp/JThirdAppDropdown'
  import mixedKeyboard from 'vehicle-keyboard/src/components/mixed-keyboard'
  export default {
    name: 'CheckSiteDataOverloadAbnormalForm',
    components: {
        JThirdAppDropdown,
        mixedKeyboard
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        isDisabled:true,
        isModVehicleNoDisabled:true,
        isModPlateColorDisabled: true,
        isModTotalDisabled: true,
        isModAxlesDisabled: true,
        isModLimitWeightDisabled: true,
        isshow:false,
        model:{},
        abnormalModel:{},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        args: {
          presetNumber: '', //预设车牌号码
          keyboardType: 0, //键盘类型[0,2]
          provinceName: '晋', //省份
          autoComplete: false
        },
        callbacks: {
          //// 回调函数
          onchanged: (presetNumber, isCompleted) => {
            if(isCompleted)
            {
                this.model.vehicleNo = presetNumber;
                this.getBlur();
            }
            console.log(
              '当前车牌[变更]：' + presetNumber + ', 已完成:' + isCompleted
            );
          },
          onkeypressed: key => {
            console.log('当前按键：' + key.text);
          },
          oncompleted: (presetNumber, isAutoCompleted) => {
            this.numberArray = presetNumber.split('');
            this.model.vehicleNo = presetNumber;
            this.getBlur();
            console.log(
              '当前车牌[完成]：' + presetNumber + ', 自动完成:' + isAutoCompleted
            );
          },
          onmessage: function(message) {
            //console.info(message);
          }
        },
        confirmLoading: false,
        validatorRules: {
          siteCode: [
              { required: true, message: '请输入站点编号!'},
           ],
           vehicleNo: [
              { required: true, message: '请输入车辆号牌!'},
           ],
           plateColor: [
              { required: true, message: '请输入车牌颜色!'},
           ],
           limitWeight: [
              { required: true, message: '请输入最大允许总质量!'},
           ],
           overWeight: [
              { required: true, message: '请输入超限量!'},
           ],
           overRate: [
              { required: true, message: '请输入超限超载率!'},
           ],
           operateType: [
              { required: true, message: '请输入操作类型!'},
           ],
        
        },
        url: {
          add: "/abnormal/abnormalSiteData/add",
          edit: "/abnormal/abnormalSiteData/edit",
          queryById: "/abnormal/abnormalSiteData/queryById",
          addAbnormal: "/abnormal/abnormalSiteData/addAbnormal",
          updateAbnormal: "/abnormal/abnormalSiteData/updateAbnormal"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
      userInfo() {
        return this.$store.getters.userInfo
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
      this.clickOther();
    },
    methods: {
      add (record) {
        this.edit(record);
      },
      //点击其他区划，键盘消失
      clickOther(){
         let _this = this;
         document.addEventListener('click',function(e){
         var inputFrom =  document.getElementById("errorPlateInput");
         if(inputFrom){
          if(!inputFrom.contains(e.target)){
            _this.isshow = false;
          }
         }
         
      });
      },
      //编辑初始化
      edit (record) {
        this.model = Object.assign({}, record);
        this.args.presetNumber = this.model.vehicleNo;
        this.$nextTick(()=>{
          this.$refs.keyboard.init();
        });
        this.visible = true;
      },
      //获得焦点显示键盘
      getFocus(){
        this.isshow = true;
      },
      // 隐藏键盘
      getBlur(){
        console.log(this.isshow);
      },
      //解析表单
      parseForm(){
         this.model.checkDataId = this.model.id;
         this.model.updateName = this.$store.getters.userInfo.username;
         delete this.model.id;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            this.parseForm();
            httpAction(this.url.addAbnormal,this.model,"post").then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
      //选择轴数
      handleUpdate(val){
          this.confirmLoading = true;
          httpAction(this.url.updateAbnormal,this.model,"post").then((res)=>{
            if(res.success){
                this.model = JSON.parse(JSON.stringify(res.result));
                this.$forceUpdate();
              }else{
                this.$message.warning(res.message);
              }
          }).finally(() => {
              this.confirmLoading = false;
            });
      },
      onChangeReason(selectedValue){
          // 4,6,2,1
          var arrReason = selectedValue.split(',');
          console.log(arrReason);
          arrReason.forEach(val => {
              switch(val){
                 case '1':
                   this.isModVehicleNoDisabled = false;
                    break;
                 case "2":
                    this.isModPlateColorDisabled = false;
                    break;
                 case "3":
                    this.isModLimitWeightDisabled = false;
                    break;
                 case "4":
                    this.isModTotalDisabled = false;
                    break;
                 case "5":
                    this.isModAxlesDisabled = false;
                    break;
                 default:
                    this.isModVehicleNoDisabled = false;


              }
          });

      }
   
    }
  }
</script>