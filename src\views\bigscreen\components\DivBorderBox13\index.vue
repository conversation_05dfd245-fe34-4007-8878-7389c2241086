<template>
    <div class="pageItemBody">
        <div class="titleInfo">{{ title }}</div>
        <div class="itemMain">
            <slot></slot>
        </div>
        <bg></bg>
    </div>
</template>
  
<script>
import bg from './bg.vue'

export default {
    name: "DivBorderBox13",
    data() {
        return {}
    },
    components: {
        bg
    },
    props: {
        title: {
            type: String,
            default() {
                return '标题';
            }
        },
    },
    watch: {},
    mounted() {
        var that = this;
    },
}
</script>
  
<style lang="less" scoped>
.pageItemBody {
    width: 100%;
    position: relative;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: nowrap;
    flex-direction: column;
    z-index: 1;
    align-content: flex-start;

    .titleInfo {
        font-size: 0.08333rem;
        font-family: PingFang;
        font-weight: bold;
        color: #FFFFFF;
        margin-left: 27px;
        padding-top: 20px;
    }
}

.itemMain {
    position: relative;
    width: 100%;
    height: calc(100% - 50px);
}
</style>