<template>
  <a-row type="flex" :gutter="16">
    <a-col :md="4" :sm="24">
      <real-time-left v-model="siteType"></real-time-left>
    </a-col>
    <a-col :md="24 - 4" :sm="24">
      <real-time-right v-model="siteCode"></real-time-right>
    </a-col>
  </a-row>
</template>

<script>
import RealTimeLeft from './modules/RealTimeLeft'
import RealTimeRight from './modules/RealTimeRight'

export default {
  name: 'RealTime',
  components: { RealTimeLeft, RealTimeRight },
  data() {
    return {
      description: '实时数据',
      siteType: '',
      siteCode: ''
    }
  },

  methods: {}
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>