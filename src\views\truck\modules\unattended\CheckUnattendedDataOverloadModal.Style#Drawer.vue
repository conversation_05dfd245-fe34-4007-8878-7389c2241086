<template>
  <a-drawer
    :title="title"
    :width="width"
    placement="right"
    :closable="false"
    @close="close"
    destroyOnClose
    :visible="visible">
    <!-- <check-unattended-data-overload-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit" normal></check-unattended-data-overload-form> -->
    <check-unattended-data-overload-detail ref="detailForm" normal></check-unattended-data-overload-detail>
    <div class="drawer-footer">
      <a-button @click="handleCancel" style="margin-bottom: 0;">关闭</a-button>
      <a-button v-if="!disableSubmit"  @click="handleOk" type="primary" style="margin-bottom: 0;">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>

  // import CheckUnattendedDataOverloadForm from './CheckUnattendedDataOverloadForm'
  import CheckUnattendedDataOverloadDetail from './CheckUnattendedDataOverloadDetail'
  export default {
    name: 'CheckUnattendedDataOverloadModal',
    components: {
      // CheckUnattendedDataOverloadForm
      CheckUnattendedDataOverloadDetail
    },
    data () {
      return {
        title:"操作",
        width:1024,
        visible: false,
        disableSubmit: false,
        detailVisible: false
      }
    },
    methods: {
         setVisible(){
        this.visible=true
        this.detailVisible = false
      },
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        });
      },
      show (record) {
        this.setVisible();
        this.detailVisible = true;
        this.$nextTick(()=>{
          this.$refs.detailForm.show(record);
        });
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>

<style lang="less" scoped>
/** Button按钮间距 */
  .ant-btn {
    margin-left: 30px;
    margin-bottom: 30px;
    float: right;
  }
  .drawer-footer{
    position: absolute;
    bottom: -8px;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 2px 2px;
  }
</style>