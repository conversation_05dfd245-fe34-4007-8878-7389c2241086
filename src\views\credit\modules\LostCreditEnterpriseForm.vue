<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="上报年份" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="year">
              <a-input v-model="model.year" placeholder="请输入上报年份"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="上报周期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="season">
              <j-dict-select-tag type="list" v-model="model.season" dictCode="season" placeholder="请选择上报周期" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="companyName">
              <a-input v-model="model.companyName" placeholder="请输入企业名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="统一社会信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unifiedCreditCode">
              <a-input v-model="model.unifiedCreditCode" placeholder="请输入统一社会信用代码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="道路运输经营许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="companyQuaCode">
              <a-input v-model="model.companyQuaCode" placeholder="请输入道路运输经营许可证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="法定代表人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="legalRepresentative">
              <a-input v-model="model.legalRepresentative" placeholder="请输入法定代表人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idCardNumber">
              <a-input v-model="model.idCardNumber" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="货运车辆总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totalNumberOfVehicles">
              <a-input-number v-model="model.totalNumberOfVehicles" placeholder="请输入货运车辆总数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="违法超限运输车辆数量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="numberOfIllegalVehicles">
              <a-input-number v-model="model.numberOfIllegalVehicles" placeholder="请输入违法超限运输车辆数量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="发布期开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="releaseStartDate">
              <j-date placeholder="请选择发布期开始日期" v-model="model.releaseStartDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="发布期结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="releaseEndDate">
              <j-date placeholder="请选择发布期结束日期" v-model="model.releaseEndDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'LostCreditEnterpriseForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           year: [
              { required: true, message: '请输入上报年份!'},
           ],
           season: [
              { required: true, message: '请输入上报周期!'},
           ],
           companyName: [
              { required: true, message: '请输入企业名称!'},
           ],
           unifiedCreditCode: [
              { required: true, message: '请输入统一社会信用代码!'},
           ],
           companyQuaCode: [
              { required: true, message: '请输入道路运输经营许可证号!'},
           ],
           legalRepresentative: [
              { required: true, message: '请输入法定代表人!'},
           ],
           idCardNumber: [
              { required: true, message: '请输入身份证号!'},
           ],
           totalNumberOfVehicles: [
              { required: true, message: '请输入货运车辆总数!'},
           ],
           numberOfIllegalVehicles: [
              { required: true, message: '请输入违法超限运输车辆数量!'},
           ],
           releaseStartDate: [
              { required: true, message: '请输入发布期开始日期!'},
           ],
           releaseEndDate: [
              { required: true, message: '请输入发布期结束日期!'},
           ],
        },
        url: {
          add: "/credit/lostCreditEnterprise/add",
          edit: "/credit/lostCreditEnterprise/edit",
          queryById: "/credit/lostCreditEnterprise/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>