<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
           <a-col :span="8">
            <a-form-model-item label="站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入站点编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入站点名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点简称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="uname">
              <a-input v-model="model.uname" placeholder="请输入站点简称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distCode">
             <j-area-linkage type="cascader" v-model="model.distCode" placeholder="请输入省市区"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检测方向" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="jcfx">
              <j-dict-select-tag  v-model="model.jcfx" dictCode="jcfx" placeholder="请输入检测方向" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="主管部门" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orgId">
              <a-input v-model="model.orgId" placeholder="请输入主管部门"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="路线标识" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="roadId">
              <a-input v-model="model.roadId" placeholder="请输入路线标识"  ></a-input>
            </a-form-model-item>
          </a-col>
           <a-col :span="8">
            <a-form-model-item label="桩号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteZh">
              <a-input v-model="model.siteZh" placeholder="请输入桩号"  ></a-input>
            </a-form-model-item>
          </a-col>
           <a-col :span="8">
            <a-form-model-item label="经度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="longitude">
              <a-input-number v-model="model.longitude" placeholder="请输入经度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="纬度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="latitude">
              <a-input-number v-model="model.latitude" placeholder="请输入纬度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteZt">
              <j-dict-select-tag  v-model="model.siteZt" dictCode="siteZt" placeholder="请选择站点状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车道数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="jccds">
              <a-input-number v-model="model.jccds" placeholder="请输入车道数" style="width: 100%"  />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
           <a-col :span="8">
            <a-form-model-item label="站点图片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zdzp">
              <j-image-upload  v-model="model.zdzp" ></j-image-upload>
            </a-form-model-item>
          </a-col>
           <a-col :span="8">
            <a-form-model-item label="联系人姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lxr">
              <a-input v-model="model.lxr" placeholder="请输入联系人姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="联系人电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lxrdh">
              <a-input v-model="model.lxrdh" placeholder="请输入联系人电话"  ></a-input>
            </a-form-model-item>
          </a-col>
           <a-col :span="8">
            <a-form-model-item label="路线名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="roadname">
              <a-input v-model="model.roadname" placeholder="请输入路线名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="电子抓拍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dzzp">
              <j-dict-select-tag type="radioButton" v-model="model.dzzp" dictCode="has_equ" placeholder="请选择是否有站前电子抓拍" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="8">
            <a-form-model-item label="站点形式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteXs">
               <j-dict-select-tag  v-model="model.siteXs" dictCode="siteXs" placeholder="请输入站点形式" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="执法方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ljfs">
              <a-input v-model="model.ljfs" placeholder="请输入执法方式"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="总人员数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zrys">
              <a-input-number v-model="model.zrys" placeholder="请输入总人员数" style="width: 100%" :min="1" :max="999"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="总执法数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zzfs">
              <a-input-number v-model="model.zzfs" placeholder="请输入总执法数" style="width: 100%" :min="1" :max="999" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="每班人员数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mbrys">
              <a-input-number v-model="model.mbrys" placeholder="请输入每班人员数" style="width: 100%" :min="1" :max="999"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="每班执法数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mbzfs">
              <a-input-number v-model="model.mbzfs" placeholder="请输入每班执法数" style="width: 100%" :min="1" :max="999" />
            </a-form-model-item>
          </a-col>
         
          <a-col :span="8">
            <a-form-model-item label="站点用地性质" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ydxz">
              <a-input v-model="model.ydxz" placeholder="请输入站点用地性质"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="占地面积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zdmj">
              <a-input v-model="model.zdmj" placeholder="请输入占地面积" style="width: 100%"  suffix="m³" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检测场地面积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="jccmj">
              <a-input v-model="model.jccmj" placeholder="请输入检测场地面积" style="width: 100%"  suffix="m³" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="房屋面积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="fwmj">
              <a-input v-model="model.fwmj" placeholder="请输入房屋面积" style="width: 100%"  suffix="m³"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="卸载厂面积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="xzcmj">
              <a-input v-model="model.xzcmj" placeholder="请输入卸载厂面积" style="width: 100%"  suffix="m³" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="总投资" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ztz">
              <a-input v-model="model.ztz" placeholder="请输入总投资" style="width: 100%"  suffix="¥"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="资金来源" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zjly">
              <a-input v-model="model.zjly" placeholder="请输入资金来源"  ></a-input>
            </a-form-model-item>
          </a-col>
          
         
          <a-col :span="8">
            <a-form-model-item label="投入运行日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tryxrq">
              <j-date placeholder="请选择投入运行日期" v-model="model.tryxrq"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点级别编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zdjbbm">
              <j-dict-select-tag  v-model="model.zdjbbm" dictCode="zdjbbm" placeholder="请选择站点级别编码" />
            </a-form-model-item>
          </a-col>
         
          <a-col :span="8">
            <a-form-model-item label="部级系统编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ministerialOerloadCode">
              <a-input v-model="model.ministerialOerloadCode" placeholder="请输入部级系统编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点IP" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ip">
              <a-input v-model="model.ip" placeholder="请输入站点IP"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="所属部门" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sysOrgCode">
              <j-select-depart v-model="model.sysOrgCode" :trigger-change="true" customReturnField="orgCode" :multi="true"  />
            </a-form-model-item>
          </a-col>
           <a-col :span="8">
            <a-form-model-item label="详细地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-textarea v-model="model.address" rows="4" placeholder="请输入详细地址" />
            </a-form-model-item>
          </a-col>
           <a-col :span="8">
            <a-form-model-item label="备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
              <a-textarea v-model="model.remark" rows="4" placeholder="请输入备注" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseSiteForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
          dzzp:1,
          
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           code: [
              { required: true, message: '请输入站点编码!'},
              { pattern: '[\w.%+-]', message: '不符合校验规则!'},
              { validator: (rule, value, callback) => validateDuplicateValue('base_site', 'code', value, this.model.id, callback)},
           ],
           name: [
              { required: true, message: '请输入站点名称!'},
           ],
           uname: [
              { required: true, message: '请输入站点简称!'},
           ],
           distCode: [
              { required: true, message: '请输入行政区划!'},
           ],
           jcfx: [
              { required: true, message: '请输入检测方向!'},
           ],
           siteZt: [
              { required: true, message: '请输入站点状态!'},
           ],
           jccds: [
              { required: true, message: '请输入车道数!'},
           ],
           zrys: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
           zzfs: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
           mbrys: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
           mbzfs: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
           lxr: [
              { required: false},
              { pattern: /^-?\d+$/, message: '请输入整数!'},
           ],
           lxrdh: [
              { required: false},
              { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!'},
           ],
           zdmj: [
              { required: false},
              {  pattern: /\d+$/, message: '请输入正整数!'},
           ],
           jccmj: [
              { required: false},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           fwmj: [
              { required: false},
              { pattern: /^\d+\.?\d*$/, message: '请输入数字!'},
           ],
           xzcmj: [
              { required: false},
              { pattern: /^-?\d+\.?\d*$/, message: '请输入数字!'},
           ],
           ztz: [
              { required: false},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
           ],
           zjly: [
              { required: false},
              { pattern: /^(([1-9][0-9]*)|([0]\.\d{0,2}|[1-9][0-9]*\.\d{0,2}))$/, message: '请输入正确的金额!'},
           ],
           dzzp: [
              { required: true, message: '请输入是否有站前电子抓拍!'},
           ],
           tryxrq: [
              { required: true, message: '请输入投入运行日期!'},
           ],
           zdjbbm: [
              { required: true, message: '请输入站点级别编码!'},
           ],
           ip: [
              { required: true, message: '请输入站点IP!'},
           ],
        },
        url: {
          add: "/system/baseSite/add",
          edit: "/system/baseSite/edit",
          queryById: "/system/baseSite/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>