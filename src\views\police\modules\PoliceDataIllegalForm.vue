<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="违法序号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfxh">
              <a-input-number v-model="model.wfxh" placeholder="请输入违法序号" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="电子抓拍设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zpsbbh">
              <a-input v-model="model.zpsbbh" placeholder="请输入电子抓拍设备编号"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="号牌种类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hpzl">
              <a-input v-model="model.hpzl" placeholder="请输入号牌种类"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="号牌号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hphm">
              <a-input v-model="model.hphm" placeholder="请输入号牌号码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车辆类型名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cllxmc">
              <a-input v-model="model.cllxmc" placeholder="请输入车辆类型名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="发现机关名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="fxjgmc">
              <a-input v-model="model.fxjgmc" placeholder="请输入发现机关名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="机动车所有人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="jdcsyr">
              <a-input v-model="model.jdcsyr" placeholder="请输入机动车所有人"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lxdh">
              <a-input v-model="model.lxdh" placeholder="请输入联系电话"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="所有人身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="syrsfzh">
              <a-input v-model="model.syrsfzh" placeholder="请输入所有人身份证号"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="违法行为" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfxw">
              <a-input v-model="model.wfxw" placeholder="请输入违法行为"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="违法行为描述" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfxwms">
              <a-input v-model="model.wfxwms" placeholder="请输入违法行为描述"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="违法地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfdz">
              <a-input v-model="model.wfdz" placeholder="请输入违法地址"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="违法时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfsj">
              <j-date placeholder="请选择违法时间" v-model="model.wfsj" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="罚款金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="fkje">
              <a-input-number v-model="model.fkje" placeholder="请输入罚款金额" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label=" 违法记分数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfjfs">
              <a-input-number v-model="model.wfjfs" placeholder="请输入 违法记分数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否有效" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sfyx">
              <a-input-number v-model="model.sfyx" placeholder="请输入是否有效" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="无效原因描述" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wxyyms">
              <a-input v-model="model.wxyyms" placeholder="请输入无效原因描述"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="处理标记" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="clbj">
              <a-input v-model="model.clbj" placeholder="请输入处理标记"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="处理机关名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cljgmc">
              <a-input v-model="model.cljgmc" placeholder="请输入处理机关名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="处理时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="clsj">
              <j-date placeholder="请选择处理时间" v-model="model.clsj" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label=" 当事人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dsr">
              <a-input v-model="model.dsr" placeholder="请输入 当事人"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="当事人身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dsrsfzh">
              <a-input v-model="model.dsrsfzh" placeholder="请输入当事人身份证号"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="更新时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="gxsj">
              <j-date placeholder="请选择更新时间" v-model="model.gxsj" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'PoliceDataIllegalForm',
  components: {
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      model: {
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      validatorRules: {
      },
      url: {
        add: "/police/policeDataIllegal/add",
        edit: "/police/policeDataIllegal/edit",
        queryById: "/police/policeDataIllegal/queryById"
      }
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model));
  },
  methods: {
    add() {
      this.edit(this.modelDefault);
    },
    edit(record) {
      this.model = Object.assign({}, record);
      this.visible = true;
    },
    submitForm() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
          })
        }

      })
    },
  }
}
</script>