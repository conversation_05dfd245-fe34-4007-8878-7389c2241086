<template>
  <div :style="{ padding: '0 0 32px 32px' }">
    <h4 :style="{ marginBottom: '20px' }">{{ title }}</h4>
    <v-chart :forceFit="true" :height="height" :data="dv" :scale="scale" :onClick="handleClick">
      <v-tooltip />
      <v-axis />
      <v-legend />
      <v-bar position="x*y" color="type" :adjust="adjust" />
    </v-chart>
    <v-plugin>
      <v-slider width="auto" :height="26" container='viser-slider-1' :start="start" :end="end" :data="dataSource"
        x-axis="name" y-axis="overnum" :backgroundChart="bg" :onChange="onChange" />
    </v-plugin>
  </div>
</template>

<script>
import { DataSet } from '@antv/data-set'
import { triggerWindowResizeEvent } from '@/utils/util'
import { ChartEventMixins } from './mixins/ChartMixins'

export default {
  name: "BarMultidSlider",
  mixins: [ChartEventMixins],
  props: {
    title: {
      type: String,
      default: "Demo"
    },
    height: {
      type: Number,
      default: 254
    },
    dataSource: {
      type: Array,
      default: () => [
        { name: 'Jeecg', 'num': 1, 'overnum': 6 },
        { name: 'Jeebt', 'num': 2, 'overnum': 7 },
        { name: 'Jeeb1', 'num': 3, 'overnum': 8 },
        { name: 'Jeeb2', 'num': 4, 'overnum': 9 },
        { name: 'Jeeb3', 'num': 5, 'overnum': 10 }
      ]
    },
    // fields: {
    //   type: Array,
    //   default: () => ['Jeecg', 'Jeebt', 'Jeeb1', 'Jeeb2', 'Jeeb3']
    // },
    // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]
    aliases: {
      type: Array,
      default: () => [{ field: 'num', alias: '过车数' }, { field: 'over', alias: '超载数' }]
    },
  },
  mounted() {
    triggerWindowResizeEvent()
    const { dv, ds } = this.getData();
    this.$data.dv = dv;
    this.$data.start = ds.state.start;
    this.$data.end = ds.state.end;
  },
  computed: {
    scale() {
      return [
        {
          type: 'cat',
          dataKey: 'x'
        }
      ]
    }
  },
  data() {
    return {
      adjust: [{
        type: 'dodge',
        marginRatio: 1 / 32
      }],
      start: "Jeecg",
      end: "Jeebt",
      bg: { type: 'line' },
      dv: {},
      fields: []
    }
  },
  created() {
    // this.start = this.dataSource[0].name;
    // if (this.dataSource.length >= 10) {
    //   this.end = this.dataSource[10].name;
    // } else {
    //   this.end = this.dataSource[this.dataSource.length].name;
    // }

  },
  watch: {
    dataSource: function () {
      console.log(this.dataSource.length);
      if(this.dataSource.length <= 0){

          return;
      }
      this.start = this.dataSource[0].name;
      if (this.dataSource.length >= 10) {
        this.end = this.dataSource[10].name;
      } else {
        this.end = this.dataSource[this.dataSource.length-1].name;
      }
      triggerWindowResizeEvent()
      const { dv, ds } = this.getData();
      this.$data.dv = dv;
      this.$data.start = ds.state.start;
      this.$data.end = ds.state.end;
    }
  },
  methods: {
    getData() {
      const { dataSource, start, end } = this;
      var data = this.convertDataSource(dataSource);
      var startIndex;
      var endIndex;
      dataSource.forEach(function (item, index) {
        if (item.name == start) {
          startIndex = index;
        }
        if (item.name == end) {
          endIndex = index;
        }
      });
      const ds = new DataSet({
        state: {
          start: startIndex,
          end: endIndex
        }
      });
      const dv = ds.createView("origin").source(data);
      dv.transform({
        type: 'fold',
        fields: this.fields,
        key: 'x',
        value: 'y',
        // retains: [ 'num', 'over','name']
      })
      dv.transform({
        type: "filter",
        callback: function callback(obj, index) {
          var dataSize = dataSource.length;
          return (index >= ds.state.start && index <= ds.state.end) || (index >= ds.state.start + dataSize && index <= ds.state.end + dataSize);

        }
      });

      // bar 使用不了 - 和 / 所以替换下
      let rows = dv.rows.map(row => {
        if (typeof row.x === 'string') {
          row.x = row.x.replace(/[-/]/g, '_')
        }
        return row
      })
      // 替换别名
      rows.forEach(row => {
        for (let item of this.aliases) {
          if (item.field === row.type) {
            row.type = item.alias
            break
          }
        }
      })
      return { dv, ds };
    },
    onChange(_ref) {
      var startValue = _ref.startValue, endValue = _ref.endValue;
      this.start = startValue;
      this.end = endValue;
      this.dv = this.getData().dv;
    },
    convertDataSource(data) {
      if (data.length != 0) {
        var fieldsArr = [];
        var numArr = [];
        var overNumArr = [];
        for (let i = 0; i < data.length; i++) {
          fieldsArr.push(data[i].name)
          numArr.push(data[i].num);
          overNumArr.push(data[i].over_num);
        }
        this.fields = fieldsArr;
        var numData = { type: "num" };
        var overData = { type: "over" };
        for (let j = 0; j < fieldsArr.length; j++) {
          numData[fieldsArr[j]] = numArr[j];
          overData[fieldsArr[j]] = overNumArr[j];
        }
        var countSource = [];
        countSource.push(numData);
        countSource.push(overData);
        return countSource;
      }
    }
  }

}
</script>

<style scoped>
</style>