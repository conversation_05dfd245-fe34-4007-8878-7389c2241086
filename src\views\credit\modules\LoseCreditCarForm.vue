<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="上报年份" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="year">
              <a-input-number v-model="model.year" placeholder="请输入上报年份" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="上报周期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="season">
              <j-dict-select-tag type="list" v-model="model.season" dictCode="season" placeholder="请选择上报周期" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车牌号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNo">
              <a-input v-model="model.vehicleNo" placeholder="请输入车牌号码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="道路运输证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="roadTransportNumber">
              <a-input v-model="model.roadTransportNumber" placeholder="请输入道路运输证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="违法次数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="numberOfIllegal">
              <a-input-number v-model="model.numberOfIllegal" placeholder="请输入违法次数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="违法案件信息列表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="caseList">
                <j-popup
                v-model="model.caseList"
                field="caseList"
                org-fields="code"
                dest-fields="caseList"
                code="case"
                :multi="true"
                @input="popupCallback"
                />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="发布期开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="releaseStartDate">
              <j-date placeholder="请选择发布期开始日期" v-model="model.releaseStartDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="发布期结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="releaseEndDate">
              <j-date placeholder="请选择发布期结束日期" v-model="model.releaseEndDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'LoseCreditCarForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           year: [
              { required: true, message: '请输入上报年份!'},
           ],
           season: [
              { required: true, message: '请输入上报周期!'},
           ],
           vehicleNo: [
              { required: true, message: '请输入车牌号码!'},
           ],
           roadTransportNumber: [
              { required: true, message: '请输入道路运输证号!'},
           ],
           numberOfIllegal: [
              { required: true, message: '请输入违法次数!'},
           ],
           caseList: [
              { required: true, message: '请输入违法案件信息列表!'},
           ],
           releaseStartDate: [
              { required: true, message: '请输入发布期开始日期!'},
           ],
           releaseEndDate: [
              { required: true, message: '请输入发布期结束日期!'},
           ],
        },
        url: {
          add: "/credit/loseCreditCar/add",
          edit: "/credit/loseCreditCar/edit",
          queryById: "/credit/loseCreditCar/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
       popupCallback(value,row){
         this.model = Object.assign(this.model, row);
         console.log(row);
      },
    }
  }
</script>