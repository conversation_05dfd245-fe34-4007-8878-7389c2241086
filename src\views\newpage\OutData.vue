<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业名称">
              <a-input placeholder="请输入企业名称" v-model="queryParam.enterpriseCode"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="车牌号">
              <a-input placeholder="请输入车牌号" v-model="queryParam.checkNo"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="入厂时间">
                <a-input placeholder="请输入入厂时间" v-model="queryParam.vehicle"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- 高级查询区域 -->
      <j-super-query
        :fieldList="superFieldList"
        ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <over-weight-data-modal ref="modalForm" @ok="modalFormOk"></over-weight-data-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import OverWeightDataModal from './modules/OverWeightDataModal'
  import {getAction} from '@/api/manage'
export default {
  name: 'OutData',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    OverWeightDataModal,
  },
  data() {
    return {
      description: '称重数据表管理页面',
      // 表头
      columns: [
        {
          title: '企业名称',
          align: 'center',
          dataIndex: 'enterpriseName',
        },
        {
          title: '企业编码',
          align: 'center',
          dataIndex: 'enterpriseCode',
        },
        {
          title: '门禁编码',
          align: 'center',
          dataIndex: 'doorCode',
        },
        {
          title: '门禁抓拍编码',
          align: 'center',
          dataIndex: 'doorCameraCode',
        },
        {
          title: '车牌号',
          align: 'center',
          dataIndex: 'vehicleNo',
          slots: { customRender: 'plate' },
        },
        // {
        //   title: '车辆图片',
        //   align: 'center',
        //   dataIndex: '',
        //   customRender: ({ record }) => {
        //     let img = ''
        //     record.checkEnterpriseDoorImagesList.forEach((item) => {
        //       img = item.dataUrl
        //     })
        //     return render.renderImage({ text: img })
        //   },
        // },
        {
          title: '出厂时间',
          align: 'center',
          sorter: true,
          dataIndex: 'exitTime',
        },
        {
          title: '出厂图片',
          align: 'center',
          dataIndex: 'exitImage',
          scopedSlots: { customRender: 'imgSlot' },
          width: 120,
        },
      ],
      url: {
        list: '/door/checkEnterpriseDoorExit/list',
        delete: '/overweight/overWeightData/delete',
        deleteBatch: '/overweight/overWeightData/deleteBatch',
        exportXlsUrl: '/overweight/overWeightData/exportXls',
        importExcelUrl: 'overweight/overWeightData/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
      dataSource:[]
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    loadData() {
        if (!this.url.list) {
          this.$message.error("请设置url.list属性!")
          return
        }
        let params = this.getQueryParams();//查询条件
        params.column = 'exitTime';
        getAction(this.url.list, params).then((res) => {
          if (res.success && res.result) {
            this.dataSource = res.result.records;
            this.ipagination.total = res.result.total;
          }
        })
      },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'enterpriseCode', text: '企业编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'checkNo', text: '检测单号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'equipCode', text: '设别编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicle', text: '车头车牌号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'color', text: '车头车牌颜色', dictCode: '' })
      fieldList.push({ type: 'string', value: 'tailVehicle', text: '车尾车牌号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'tailColor', text: '车尾车牌颜色', dictCode: '' })
      fieldList.push({ type: 'string', value: 'landId', text: '车道号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'axles', text: '轴数', dictCode: '' })
      fieldList.push({ type: 'string', value: 'axleType', text: '轴型', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicleType', text: '车型', dictCode: '' })
      fieldList.push({ type: 'string', value: 'limitWeight', text: '限重', dictCode: '' })
      fieldList.push({ type: 'string', value: 'total', text: '总重', dictCode: '' })
      fieldList.push({ type: 'string', value: 'overWeight', text: '超重', dictCode: '' })
      fieldList.push({ type: 'string', value: 'overRate', text: '超载率', dictCode: '' })
      fieldList.push({ type: 'string', value: 'overFlag', text: '超载标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'direction', text: '出/入厂称重', dictCode: '' })
      fieldList.push({ type: 'string', value: 'headImage', text: '车头照片', dictCode: '' })
      fieldList.push({ type: 'string', value: 'tailImage', text: '车尾照片', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
