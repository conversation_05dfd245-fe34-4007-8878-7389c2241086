<template>
    <div class="map_container">
        <div ref="myEchart" class="map-echart">
        </div>
        <div class="btn_container">
            <div :class="default_btn == 'site' ? 'btn btn_active' : 'btn'" @click="tabsClick('site')">
                站点
            </div>
            <div :class="default_btn == 'enterprise' ? 'btn btn_active' : 'btn'" @click="tabsClick('enterprise')">
                源头企业
            </div>
            <div :class="default_btn == 'unmanned' ? 'btn btn_active' : 'btn'" @click="tabsClick('unmanned')">
                无人站点
            </div>
        </div>
        <Popup :title='title' ref="popup">
            
        </Popup>
        <county title="站点详情" ref="popupDetail"/>

    </div>
</template>
   
<script>
import Popup from '@/views/bigscreen/components/Popup/index'
import county from '../components/Popup/county.vue'
import { getAction } from '@/api/manage'
export default {
    name: 'CenterMap',
    components: {
        Popup,
        county
    },
    data() {
        return {
            myChartMap: '',
            default_btn: "site",
            options: {},
            url: {
                chartUrl: "/bigScreen/siteMapDataNew"
            },
            seriesData: [],
            title:'站点详情'
        }
    },
    mounted() {
        const uploadedDataURL = require(`@/assets/map/data/jincheng.json`)
        this.$echarts.registerMap('jincheng', uploadedDataURL)
        this.myChartMap = this.$echarts.init(this.$refs.myEchart)
        this.myChartMap.off('click');
        this.myChartMap.on('click', params => {
            // if (params.seriesType === 'scatter') {
                this.powerClick(params)
            // }

        })
        this.initEcharts();
        this.getData(this.default_btn);
       
    },
    methods: {
        initEcharts() {
            this.$nextTick(() => {
                this.options = {
                    // 悬浮窗
                    // tooltip: {
                    //     showContent: true,
                    //     trigger: 'item',
                    //     triggerOn: "click",
                    //     enterable: false,
                    //     extraCssText: 'border:1px solid transparent',
                    //     renderMode:"auto",
                    //     formatter: function loadData(params) {
                    //         return `<div  class='Mo_ban'>
                    //                      <div id='Mo_ban_box'>
                    //                         <div>站点名称：${params.data.name}</div>
                    //                         <div>站点状态：${params.data.netStatus}</div>
                    //                         <div id="Mo_ban_look" onclick="goToDetail('${params.data.code}')">查看详情</div>
                    //                     </div>
                    //                 </div>`;
                    //     }
                    // },
                    geo: [{
                        //第一层需要操作点击展示的地图
                        show: true,
                        map: 'jincheng',
                        z: 1,
                        aspectScale: 1,
                        zoom: 1.15,
                        roam: false,
                        label: {
                            show: true,
                            color: '#fff',
                            postion: 'inside',
                            distance: 0,
                            fontSize: 12
                        },
                        itemStyle: {
                            normal: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    x2: 0,
                                    y: 0,
                                    y2: 1,
                                    colorStops: [
                                        { offset: 0, color: '#0843a9' },
                                        { offset: 0.9, color: '#001c80' }
                                    ]
                                },
                                borderWidth: 1,
                                borderColor: '#55bef2'
                            }
                        },
                        emphasis: {
                            label: {
                                show: true,//是否显示高亮
                                textStyle: {
                                    color: '#fff',
                                    fontSize: 15  //高亮放大字体
                                }
                            },
                            itemStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    x2: 0,
                                    y: 0,
                                    y2: 1,
                                    colorStops: [
                                        { offset: 0, color: '#12F5D9' },
                                        { offset: 0.49, color: '#08BCEE' },
                                        { offset: 0.5, color: '#08BCEE' },
                                        { offset: 1, color: '#0194FD' }
                                    ]
                                },
                                shadowColor: 'rgba(255,248,74,0.8)',//阴影颜色
                                shadowBlur: 3,//阴影大小
                                shadowOffsetX: 10,//沿x轴宽度 
                                shadowOffsetY: 10,//沿y轴宽度
                                borderCap: 'square'
                            }
                        },
                    },
                    {
                        show: false,
                        map: 'jincheng',
                        z: 0,
                        top: 75,
                        left: 100,
                        aspectScale: 1,
                        zoom: 1.15,
                        roam: false,
                        label: {
                            normal: {
                                show: false
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        itemStyle: {
                            normal: {
                                areaColor: 'rgba(5, 32, 136, 1)',
                                borderColor: 'transparent',
                                borderWidth: 5
                            },
                            emphasis: {
                                areaColor: 'rgba(5, 32, 136, 1)'
                            }
                        }
                    }
                    ],
                    series: [
                        {
                            type: 'scatter',
                            itemStyle: {
                                opacity: 1,
                                shadowBlur: 10,
                                shadowOffsetX: 1,
                                shadowOffsetY: 1
                            },
                            // symbol: "image://" + process.env.BASE_URL + "marker_site.svg",
                            symbol: function (params) {
                                switch (params[2]) {
                                    case 'site':
                                        return "image://" + process.env.BASE_URL + "marker_site.svg";
                                    case 'enterprise':
                                        return "image://" + process.env.BASE_URL + "marker_enterprise.svg";
                                    case 'unmanned':
                                        return "image://" + process.env.BASE_URL + "marker_unattended.svg";
                                }

                            },
                            coordinateSystem: 'geo',
                            zlevel: 101,
                            geoIndex: 0,
                            top: 10,
                            symbolSize: 35,
                            data: this.seriesData,
                        },

                    ]
                }
                this.myChartMap.setOption(this.options)
            });
        },
        tabsClick(value) {
            this.default_btn = value;
            switch (value) {
                case "site":
                    this.getData(value);
                    break;
                case "enterprise":
                    this.getData(value);
                    break;
                case "unmanned":
                    this.getData(value);
                    break;

            }
        },
        getData(value) {
            getAction(this.url.chartUrl, { type: value }).then((res) => {
                this.options.series[0].data = [];
                this.seriesData = [];
                (res.result).forEach(data => {
                    this.seriesData.push({
                        name: data.name,
                        value: [data.lnt, data.lat, data.type],
                        code: data.siteCode,
                        zt: data.siteZt,
                        netStatus: data.siteStatus,
                        type:data.type
                    });
                });
                this.options.series[0].data = this.seriesData;
                this.myChartMap.setOption(this.options)
            });
        },
     
        powerClick(value) {
            if(value.data){
                let code = (value.data && value.data.code) || 0;
                if(value.data.type == 'site'){
                    this.title = '站点详情'

                }else if(value.data.type == 'enterprise'){
                    this.title = '源头企业详情'
                }else{
                    this.title = '无人站点详情'
                }
                this.$refs.popup.getShow(code,7,(value.data && value.data.type) || 'County');
            }else{
                this.$refs.popupDetail.getShow(value.name);
            }
         
           
        }
    }


}
</script>
   
<style lang="less">
.map_container {
    position: relative;

    .map-echart {
        height: 33rem /* 600/16 */;
        width: 100%;
    }

    .btn_container {
        position: absolute;
        display: flex;
        flex: 1;
        flex-direction: column;
        bottom: 20px;
        right: 5px;

        .btn {
            text-align: center;
            background-color: transparent;
            color: #fff;
            border-radius: 5px;
            border: 1px solid #004dc0;
            padding: 10px 20px;
            font-size: 16px;
            // box-shadow: inset 0 0 30px #0095ff;
            margin: 5px 0 0 0;
            cursor: pointer;

            &:hover,
            :focus {
                box-shadow: inset 0px 0px 28px #175acc;
                border: 1px solid #004dc0;
            }
        }

        ;

        .btn_active {
            box-shadow: inset 0px 0px 28px #175acc;
            border: 1px solid #004dc0;
        }

    }

}

.site_content {
    display: flex;
    flex-direction: column;
    height:20rem /* 400/16 */ /* 440/16 */;

    .info_top {
        width: 100%;
        flex: 1;
    }

    .info_bottom {
        width: 100%;
        height: 100%;
        margin-top: 10px;
        flex: 3;
        display: flex;
    }
}

.Mo_ban {
    #Mo_ban_look {
        cursor: pointer;
        font-size: 14px;
        font-weight: bolder;
        margin-top: 10px;
    }
}

.ant-descriptions-title {
    color: white;
}

.ant-descriptions-bordered .ant-descriptions-view {
    // border:1px so;
}

.ant-descriptions-bordered .ant-descriptions-row {
    // border-bottom: none
}

.ant-descriptions-bordered .ant-descriptions-item-label {
    background-color: transparent;
}

.ant-descriptions-item-label {
    color: white;
}

.ant-descriptions-item-content {
    color: white;
}</style>
   