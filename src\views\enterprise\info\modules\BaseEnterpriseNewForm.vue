<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入企业名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="企业编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入企业编码" disabled ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="简称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="shortName">
              <a-input v-model="model.shortName" placeholder="请输入简称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="信用编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sourceCode">
              <a-input v-model="model.sourceCode" placeholder="请输入信用编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="行政编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distCode">
             <j-area-linkage type="cascader" v-model="model.distCode" placeholder="请输入省市区"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="企业类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <j-dict-select-tag type="list" v-model="model.type" dictCode="enterprise_type_new" placeholder="请选择企业类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-input v-model="model.address" placeholder="请输入地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="邮编" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="postCode">
              <a-input v-model="model.postCode" placeholder="请输入邮编"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="负责人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="personInChange">
              <a-input v-model="model.personInChange" placeholder="请输入负责人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="联系人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="contactor">
              <a-input v-model="model.contactor" placeholder="请输入联系人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="telephone">
              <a-input v-model="model.telephone" placeholder="请输入联系电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="传真" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="chuanZhen">
              <a-input v-model="model.chuanZhen" placeholder="请输入传真"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否安装称重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasWeighting">
              <j-switch v-model="model.hasWeighting"  ></j-switch>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否安装抓拍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasCamera">
              <j-switch v-model="model.hasCamera"  ></j-switch>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否联网" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasNetwork">
              <j-switch v-model="model.hasNetwork"  ></j-switch>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否使用电子运单" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasYundan">
              <j-switch v-model="model.hasYundan"  ></j-switch>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="主要货物类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hl">
              <j-multi-select-tag type="list_multi" v-model="model.hl" dictCode="base_goods,name,code" placeholder="请选择主要货物类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="经度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="longitude">
              <a-input-number v-model="model.longitude" placeholder="请输入经度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="纬度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="latitude">
              <a-input v-model="model.latitude" placeholder="请输入纬度"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="部级编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ministerialOerloadCode">
              <a-input v-model="model.ministerialOerloadCode" placeholder="请输入部级编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否重点企业" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isFocus">
              <j-switch v-model="model.isFocus"  ></j-switch>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="列为重点日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="focusDate">
              <j-date placeholder="请选择列为重点日期" v-model="model.focusDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="数据状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dataStatus">
              <j-dict-select-tag type="list" v-model="model.dataStatus" dictCode="data_status" placeholder="请选择数据状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="企业状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag type="list" v-model="model.status" dictCode="enterprise_status" placeholder="请选择企业状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reviewTime">
              <j-date placeholder="请选择审核时间"  v-model="model.reviewTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否审核" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="izReview">
              <a-input v-model="model.izReview" placeholder="请输入是否审核"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reviewBy">
              <a-input v-model="model.reviewBy" placeholder="请输入审核人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="拒绝理由" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reviewStopRemark">
              <a-input v-model="model.reviewStopRemark" placeholder="请输入拒绝理由"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="最后数据时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lastDataTime">
              <j-date placeholder="请选择最后数据时间"  v-model="model.lastDataTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseEnterpriseNewForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/info/baseEnterprise/add",
          edit: "/info/baseEnterprise/edit",
          queryById: "/info/baseEnterprise/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>