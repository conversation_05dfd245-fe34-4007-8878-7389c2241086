<template>
  <div>
    <div class="popWin">
      <div class="popWinBg" v-show="show"></div>
      <div class="popWinMain" ref="popWinMain" v-show="show">
        <div class="titleBody" ref="titleBody">
          <span>{{ title }}</span>
        </div>
        <div class="popClose" ref="popClose" v-if="showInner" @click="getHide"></div>
        <div class="slot" v-if="showInner">
          <div class="site_content">
            <!-- <dv-loading>Loading...</dv-loading> -->
            <div class="info_top">
              <a-descriptions :title="info.name" bordered :column="{ xxl: 3, xl: 3, lg: 3, md: 3, sm: 3, xs: 3 } "
                size="middle">
                <a-descriptions-item label="状态">
                  {{ info.status}}
                </a-descriptions-item>
                <a-descriptions-item label="网络状态">
                  {{ info.net_status}}
                </a-descriptions-item>
                <a-descriptions-item label="数据状态">
                  {{ info.data_status}}
                </a-descriptions-item>
                <a-descriptions-item label="今日过车数">
                  {{ info.today_check_num}}辆
                </a-descriptions-item>
                <a-descriptions-item label="今日超载数">
                  {{ info.today_check_over_num}}辆
                </a-descriptions-item>
                <a-descriptions-item label="今日超载率">
                  {{ info.today_over_num.toFixed(4)}}%
                </a-descriptions-item>
                
              </a-descriptions>

            </div>
            <div class="info_bottom">
              <site-line-chart ref="chart"></site-line-chart>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script>
import gsap from 'gsap'
import SiteLineChart from '@/views/bigscreen/modules/SiteLineChart'
import { getAction } from '@/api/manage'
export default {
  data() {
    return {
      show: false,
      showInner: false,
      url: {
        site_info: "/bigScreen/siteDetail",
        enterprise_info: "/bigScreen/enterpriseDetail",
        unmanned_info: "/bigScreen/unmannedDetail"
      },
      info: {
        status: "运行中",
        net_status: "正常",
        name: "",
        data_status:"异常",
        today_check_num: 0,
        today_check_over_num: 0,
        today_over_num:0.0000 ,
      }
    }
  },
  components: { gsap, SiteLineChart },
  computed: {},
  mounted() {
    // this.getShow()
  },
  props: {
    title: {
      type: String,
      default() {
        return '标题';
      }
    }
  },
  methods: {
    getShow(code, offset, type) {
      this.show = true
      gsap.to(this.$refs.popWinMain, {
        duration: 1, width: '50%', onComplete: () => {
          this.showInner = true
          this.$nextTick(() => {
            gsap.to(this.$refs.popClose, {
              duration: 1, transform: ' rotateX(180deg)', onComplete: () => {
                this.showInner = true
                this.getData(code, offset, type);
              }
            })
            gsap.to(this.$refs.titleBody, {
              duration: 1, opacity: 1, top: 1, onComplete: () => {
                this.showInner = true
              }
            })
          })
        }
      })
    },
    getHide() {
      this.showInner = false
      gsap.to(this.$refs.titleBody, {
        duration: 1, opacity: 0, onComplete: () => {
          this.showInner = false
        }
      })
      this.$nextTick(() => {
        gsap.to(this.$refs.popWinMain, {
          duration: 1, width: '0%', onComplete: () => {
            this.show = false
          }
        })
      })
    },
    getInfo(code, type) {
      var url = "";
      if (type == "site") {
        url = this.url.site_info;
      } else if (type == "enterprise") {
        url = this.url.enterprise_info;
      } else if (type == "unmanned") {
        url = this.url.unmanned_info;
      }
      getAction(url, { code: code }).then((res) => {
        if (res.success) {
          console.log("结果",res.result);
            this.info.status = res.result.siteZt,
            this.info.net_status = res.result.netStatus,
            this.info.name = res.result.siteName,
            this.info.today_check_num = res.result.todayCheckNum,
            this.info.today_check_over_num = res.result.todayOverNum,
            this.info.today_over_num = res.result.todayOverRate || 0.0000,
            this.info.data_status = res.result.dataStatus
            console.log("this.info",this.info);
        }

      });
    },
    getData(code, offset, type) {
      this.$nextTick(() => {
        
        this.getInfo(code, type);
        console.log(this.$refs,'this.$refs.chart', this.$refs.chart);
        this.$refs.chart.getData2(code, offset, type);
      });

    }
  },
}
</script>
  
<style lang="scss" scoped>
.popWinBg {
  position: fixed;
  z-index: 8;
  width: 100%;
  background: rgba(0, 0, 0, 0.4);
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: initial;
}

.titleBody {
  background: url(~@/assets/screen/popup/titlebg.png) center center no-repeat;
  width: 100%;
  height: 79px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  flex-direction: row;
  align-content: flex-start;
  top: -100px;
  opacity: 1;
  z-index: 0;

  span {
    font-size: 28px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 32px;
    text-shadow: 0px 2px 3px rgba(17, 20, 22, 0.31);
  }
}

.popWin {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  flex-direction: row;
  align-content: flex-start;
  position: fixed;
  z-index: 10;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  pointer-events: none;

}

.popWinMain {
  pointer-events: initial;
  background: url(~@/assets/screen/popup/popbg.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
  overflow: hidden;
  width: 0;
  max-width: 1642px;
  height: 50%;
  max-height: 1004px;
  z-index: 100;

  .slot {
    position: absolute;
    top: 80px;
    left: 30px;
    width: calc(100% - 60px);
    height: calc(100% - 100px);
    //background: red;
  }
}

.popClose {
  position: absolute;
  right: 10px;
  top: 10px;
  background: url(~@/assets/screen/popup/icon_close.png) no-repeat;
  width: 64px;
  height: 64px;
  background-size: 100% 100%;
  cursor: pointer;
  z-index: 1;
}
</style>