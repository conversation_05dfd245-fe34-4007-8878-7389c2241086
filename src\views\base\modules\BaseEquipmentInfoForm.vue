<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="车道编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkLine">
              <a-input v-model="model.checkLine" placeholder="请输入车道编号"  :disabled="IsDisable"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="所属站点" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <j-dict-select-tag type="list" v-model="model.siteCode" dictCode="base_site,name,code" placeholder="请选择所属站点" :disabled="IsDisable"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="所属站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteId">
              <a-input v-model="model.siteId" placeholder="请输入所属站点(部级编码)"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="称重设备类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipType">
              <a-input v-model="model.equipType" placeholder="请输入称重设备类型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="称重设备尺寸" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipSize">
              <a-input v-model="model.equipSize" placeholder="请输入称重设备尺寸"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检定等级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipLevel">
              <a-input v-model="model.equipLevel" placeholder="请输入称重设备检定等级"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="实现功能" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipFunc">
              <a-input v-model="model.equipFunc" placeholder="请输入称重设备实现功能"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="设备状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipStatus">
              <j-dict-select-tag type="list" v-model="model.equipStatus" dictCode="is_normal" placeholder="请选择称重设备状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="设备型号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipModel">
              <a-input v-model="model.equipModel" placeholder="请输入称重设备型号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="生产厂商" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipMaker">
              <a-input v-model="model.equipMaker" placeholder="请输入称重设备生产厂商"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="安装日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="instDate">
              <j-date placeholder="请选择称重设备安装日期" v-model="model.instDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检验周期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkZq">
              <a-input-number v-model="model.checkZq" placeholder="请输入称重设备检验周期" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检验日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkDate">
              <j-date placeholder="请选择称重设备检验日期" v-model="model.checkDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检定部门" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkDepartment">
              <a-input v-model="model.checkDepartment" placeholder="请输入称重设备计量检定部门"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="设备检定结果" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkResult">
              <a-input v-model="model.checkResult" placeholder="请输入设备检定结果"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车牌识别厂家" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recognitionCameraMaker">
              <a-input v-model="model.recognitionCameraMaker" placeholder="请输入车牌识别摄像机厂家"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车牌识别型号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recognitionCameraModel">
              <a-input v-model="model.recognitionCameraModel" placeholder="请输入车牌识别摄像机型号"  ></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item label="称重控制仪表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasWeighingControl">
                <j-dict-select-tag type="radioButton" v-model="model.hasWeighingControl" dictCode="has_equ" placeholder="请选择是否有称重控制仪表" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
            <a-form-model-item label="车辆分离器" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasVehicleSeparator">
              <j-dict-select-tag type="radioButton" v-model="model.hasVehicleSeparator" dictCode="has_equ" placeholder="请选择是否有车辆分离器" />
            </a-form-model-item>
          </a-col>
         
          </a-row>
          <a-row>
          <a-col :span="12">
            <a-form-model-item label="轮轴识别器" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasAxleRecognizer">
              <j-dict-select-tag type="radioButton" v-model="model.hasAxleRecognizer" dictCode="has_equ" placeholder="请选择是否有轮轴识别器" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="尺寸检测设备" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasMeasuringEquipment">
              <j-dict-select-tag type="radioButton" v-model="model.hasMeasuringEquipment" dictCode="has_equ" placeholder="请选择是否有尺寸检测设备" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="自动栏杆" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasAutomicRailing">
              <j-dict-select-tag type="radioButton" v-model="model.hasAutomicRailing" dictCode="has_equ" placeholder="请选择是否有自动栏杆" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="显示屏" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasVeiwingScreen">
              <j-dict-select-tag type="radioButton" v-model="model.hasVeiwingScreen" dictCode="has_equ" placeholder="请选择是否有显示屏" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="路侧单元(RSU)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hasRsu">
              <j-dict-select-tag type="radioButton" v-model="model.hasRsu" dictCode="has_equ" placeholder="请选择是否安装路侧单元（RSU）" />
            </a-form-model-item>
          </a-col>
          </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseEquipmentInfoForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
          hasWeighingControl: 1,
          hasVehicleSeparator: 1,
          hasAxleRecognizer: 1, 
          hasMeasuringEquipment:1,
          hasAutomicRailing:1,
          hasVeiwingScreen:1,
          hasRsu:1
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 7 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        queryModel:{
          code:"",
          lane:""
        },
        IsDisable:false,
        confirmLoading: false,
        validatorRules: {
           siteCode: [
              { required: true, message: '请输入所属站点!'},
           ],
           hasWeighingControl: [
              { required: true, message: '请输入是否有称重控制仪表!'},
           ],
           hasVehicleSeparator: [
              { required: true, message: '请输入是否有车辆分离器!'},
           ],
           hasAxleRecognizer: [
              { required: true, message: '请输入是否有轮轴识别器!'},
           ],
           hasMeasuringEquipment: [
              { required: true, message: '请输入是否有尺寸检测设备!'},
           ],
           hasAutomicRailing: [
              { required: true, message: '请输入是否有自动栏杆!'},
           ],
           hasVeiwingScreen: [
              { required: true, message: '请输入是否有显示屏!'},
           ],
           hasRsu: [
              { required: true, message: '请输入是否安装路侧单元（RSU）!'},
           ],
        },
        url: {
          add: "/system/baseEquipmentInfo/add",
          edit: "/system/baseEquipmentInfo/edit",
          queryById: "/system/baseEquipmentInfo/queryById",
          queryByCodeLane: "/system/baseEquipmentInfo/listEquInfo",
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      editEqu(record){
        this.visible = true;
         console.log("表单：",record);
        this.initEqu(record);
      },
      initEqu(record){
        // 获取车道设备
          const that = this;
          console.log("initEqu",record);
          this.queryModel.code = record.code;
          this.queryModel.lane = record.lane;
          console.log(this.queryModel);
          getAction(this.url.queryByCodeLane,this.queryModel).then((res)=>{
            if(res.success){
                if(res.result != null ){
                    this.model = Object.assign({}, res.result);
                }else{
                    this.model.checkLine = record.lane;
                    this.model.siteCode = record.code;
                    this.IsDisable = true;
                    this.$forceUpdate();
                }
              }
          });
          
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>