<template>
    <a-range-picker @change="handleDateChange" :size="size" :format="format" :disabled="disabled"
        :show-time="showTime" :ranges="ranges" v-model="dateVal" />
</template>
<script>
import moment from 'moment'

export default {
    name: "JDateRange",
    components: {
        moment
    },
    props: {
        size: {
            type: String,
            default: 'default',
            required: false
        },
        format: {
            type: String,
            default: 'YYYY-MM-DD HH:mm:ss',
            required: false
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false
        },
        // showTime: {
        //     type: Boolean,
        //     required: false,
        //     default: false
        // },
        ranges: {
            type: Object,
            required: false,
            default: () => ({
                今天: [moment().startOf('day'), moment()],
                昨天: [moment().startOf('day').subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
                最近三天: [moment().startOf('day').subtract(2, 'days'), moment().endOf('day')],
                最近一周: [moment().startOf('day').subtract(1, 'weeks'), moment()],
                本月: [moment().startOf('month'), moment()],
                本年: [moment().startOf('year'), moment()]
            })
        },
        dateForm: {
            type: String,
            default: 'YYYY-MM-DD HH:mm:ss',
            required: false
        },
        dateTo: {
            type: String,
            default: 'YYYY-MM-DD HH:mm:ss',
            required: false
        },
        value: {
            type: Array,
            required: false
        },
    },
    watch: {
        value(val) {
            if (val.length == 0) {
                this.dateVal = null
            } else {
                this.dateVal = val
            }
        }
    },
    created() {
    },
    data() {
        let dateStr = this.value;
        return {
            dateVal: dateStr == [] ? null : dateStr,
            showTime: {
                format: "YYYY-MM-DD HH:mm:ss",
                hideDisabledOptions: false,
                defaultValue: [
                    moment().year(moment().year()).startOf('year').valueOf(),
                    moment().year(moment().year()).endOf('year').valueOf()
                ]
            },

        }
    },

    methods: {
        moment,
        handleDateChange(mom, dateStr) {
            this.$emit('change', dateStr);
        },


    }
}
</script>
 
<style scoped></style>