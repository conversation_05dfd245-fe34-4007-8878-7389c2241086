<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="检测编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="code">
              <a-input v-model="model.code" placeholder="请输入检测编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseName">
              <a-input v-model="model.enterpriseName" placeholder="请输入企业名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="县区编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distCode">
             <j-area-linkage type="cascader" v-model="model.distCode" placeholder="请输入省市区"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="企业编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseCode">
              <a-input v-model="model.enterpriseCode" placeholder="请输入企业编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="车牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNo">
              <a-input v-model="model.vehicleNo" placeholder="请输入车牌号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateColor">
              <a-input v-model="model.plateColor" placeholder="请输入车牌颜色"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="设备编码(仪表编码)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equCode">
              <a-input v-model="model.equCode" placeholder="请输入设备编码(仪表编码)"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="车尾号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNoTail">
              <a-input v-model="model.vehicleNoTail" placeholder="请输入车尾号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="车尾颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateColorTail">
              <a-input v-model="model.plateColorTail" placeholder="请输入车尾颜色"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
              <j-dict-select-tag type="list" v-model="model.axles" dictCode="base_check_rule,axles,axles" placeholder="请选择轴数" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="轴型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axleType">
              <a-input v-model="model.axleType" placeholder="请输入轴型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="重量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight">
              <a-input-number v-model="model.weight" placeholder="请输入重量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="长" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="length">
              <a-input-number v-model="model.length" placeholder="请输入长" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="宽" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="width">
              <a-input-number v-model="model.width" placeholder="请输入宽" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="高" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="height">
              <a-input-number v-model="model.height" placeholder="请输入高" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="超宽" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overWidth">
              <a-input-number v-model="model.overWidth" placeholder="请输入超宽" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="超高" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overHeight">
              <a-input-number v-model="model.overHeight" placeholder="请输入超高" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="检测时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkTime">
              <j-date placeholder="请选择检测时间"  v-model="model.checkTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="超长" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overLength">
              <a-input-number v-model="model.overLength" placeholder="请输入超长" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="超载数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overWeight">
              <a-input-number v-model="model.overWeight" placeholder="请输入超载数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="超载率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overRate">
              <a-input-number v-model="model.overRate" placeholder="请输入超载率" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否超载" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isOverload">
              <j-dict-select-tag type="list" v-model="model.isOverload" dictCode="iz_overload" placeholder="请选择是否超载" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否完成" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="izFinish">
              <a-input v-model="model.izFinish" placeholder="请输入是否完成"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="大件运输证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licNo">
              <a-input v-model="model.licNo" placeholder="请输入大件运输证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否大件" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isBulkVehicle">
              <a-input v-model="model.isBulkVehicle" placeholder="请输入是否大件"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="检查方向" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dir">
              <a-input v-model="model.dir" placeholder="请输入检查方向"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="关联入厂code(出厂必填)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dirLinkCode">
              <a-input v-model="model.dirLinkCode" placeholder="请输入关联入厂code(出厂必填)"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="限重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitWeight">
              <a-input-number v-model="model.limitWeight" placeholder="请输入限重" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'CheckEnterpriseNewDataForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/enterprise/checkEnterpriseNewData/add",
          edit: "/enterprise/checkEnterpriseNewData/edit",
          queryById: "/enterprise/checkEnterpriseNewData/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>