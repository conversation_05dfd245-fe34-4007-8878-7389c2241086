<template>
    <div class="num_show_container">
        <div class="num_show">
            <DivBorderBox14 :title="title1">
                <div class="num_info">
                    <div class="num_container">
                        <p class="txt_p online">{{ onlineSiteNum }}</p>
                        <p class="txt_p">在线数</p>
                    </div>
                    <div class="divider"></div>
                    <div class="num_container">
                        <p class="txt_p sum">{{ siteNum }}</p>
                        <p class="txt_p">总数</p>
                    </div>
                </div>

            </DivBorderBox14>
        </div>
        <div class="num_show">
            <DivBorderBox14 :title="title3">
                <div class="num_info">
                    <div class="num_container">
                        <p class="txt_p online">{{ onlineEnterpriseNum }}</p>
                        <p class="txt_p">在线数</p>
                    </div>
                    <div class="divider"></div>
                    <div class="num_container">
                        <p class="txt_p sum">{{ enterpriseNum }}</p>
                        <p class="txt_p">总数</p>
                    </div>
                </div>
            </DivBorderBox14>
        </div>
        <div class="num_show">
            <DivBorderBox14 :title="title4">
                <div class="num_info">
                    <div class="num_container">
                        <p class="txt_p online">{{ onlineNoSiteNum }}</p>
                        <p class="txt_p">在线数</p>
                    </div>
                    <div class="divider"></div>
                    <div class="num_container">
                        <p class="txt_p sum">{{ noSiteNum }}</p>
                        <p class="txt_p">总数</p>
                    </div>
                </div>
            </DivBorderBox14>
        </div>
    </div>
</template>
<script>
import DivBorderBox13 from "@/views/bigscreen/components/DivBorderBox13/index"
import DivBorderBox14 from "@/views/bigscreen/components/DivBorderBox14/index"
export default {
    name: 'NumberShow',
    components: {
        DivBorderBox13,
        DivBorderBox14
    },
    data() {
        return {
            title1: "超限检测站数量",
            title2: "煤炭源头企业",
            title3: "源头企业",
            title4: "无人站点数量",
        }
    },
    props:{
        siteNum:{
            type: Number,
            default() {
                return 0;
            }
        },
        onlineSiteNum:{
            type: Number,
            default() {
                return 0;
            }
        },
        enterpriseNum:{
            type: Number,
            default() {
                return 0;
            }
        },
        onlineEnterpriseNum:{
            type: Number,
            default() {
                return 0;
            }
        },
        noSiteNum:{
            type: Number,
            default() {
                return 0;
            }
        },
        onlineNoSiteNum:{
            type: Number,
            default() {
                return 0;
            }
        },
        coalEnterpriseNum:{
            type: Number,
            default() {
                return 0;
            }
        },
        onlineCoalEnterpriseNum:{
            type: Number,
            default() {
                return 0;
            }
        },
    }
}
</script>
   
<style lang="less">
.num_show_container {
    display: flex;
    flex: 1;
    flex-direction: row;
    justify-content: space-between;
    padding: 0px 20px;

    .num_show {
        flex-shrink: 1;
        width: 19.75rem /* 220/16 */;
    }

    .num_info {
        display: flex;
        justify-content: center;
        text-align: center;
        .num_container{
            padding: 0px 25px;
        }
        .divider{
            display: inline-block;
            height: 2.5rem /* 40/16 */;
            width: 1px;
            background: white;
            margin-top: 10px;;
        }
        .txt_p{
            margin:0;
            font-size: .7rem /* 15/16 */;
        }
        .online{
            font-size: 1.0rem /* 24/16 */;
        }
        .sum{
            font-size: 1.0rem /* 24/16 */;
        }
    }
}
</style>
   