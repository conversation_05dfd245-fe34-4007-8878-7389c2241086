<template>
    <div class="container">
        <dv-border-box-7 class="container_box">
            <div ref="myEchart" class="chart"></div>
        </dv-border-box-7>
    </div>
</template>
  
<script>
import { getAction, deleteAction } from '@/api/manage'
import moment from 'moment'
export default {
    name: 'CountyC<PERSON>',
    components:{
        moment
    },
    data() {
        return {
            chart: null,
            options: {},
            url:{
                chartUrl:"/bigScreen/siteAxlePie"
            }
        }
    },
    mounted() {
        this.initOptions()
        this.initCharts()
        this.getData();
    },
    methods: {
        moment,
        initOptions() {
            this.options = {
                title: {
                    text: '超载轴数统计图',
                    left: 'left',
                    top: '10px',
                    left: '10px',
                    textStyle: {
                        fontSize: '15',
                        fontWeight: '600',
                        color: '#fff',
                    },
                    subtextStyle: {
                        fontSize: 12,
                        color: '#fff',
                    }

                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    left: 'right',
                    bottom: "center",
                    textStyle:{
                        color: "#fff"
                    }
                },
                series: [
                    {
                        name: '车辆轴数超载数',
                        type: 'pie',
                        radius: '50%',
                        data: [
                            { value: 2, name: '2轴' },
                            { value: 0, name: '3轴' },
                            { value: 580, name: '4轴' },
                            { value: 0, name: '5轴' },
                            { value: 300, name: '6轴' }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
        },
        initCharts() {
            this.chart = this.$echarts.init(this.$refs.myEchart)
            this.chart.setOption(this.options)
        },
        getData(){
            getAction(this.url.chartUrl,{year:moment().format("YYYY-MM-DD")}).then((res)=>{
                this.options.series[0].data = res.result;
                this.chart.setOption(this.options)
            });
        }
    }

}
</script>
  
<style lang="less">
.container {
    position: relative;

    // background: red;
    .container_box {
        width: 95%;
        height: 95%;
        margin: 0 auto;

        .chart {
            width: 100%;
            height: 100%;
        }
    }

}
</style>
  