<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="8">
            <a-form-model-item label="uuid" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="uniqueId">
              <a-input v-model="model.uniqueId" placeholder="请输入uuid"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检测单号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkNo">
              <a-input v-model="model.checkNo" placeholder="请输入检测单号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="治超站" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteName">
              <a-input v-model="model.siteName" placeholder="请输入治超站"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="站点编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <a-input v-model="model.siteCode" placeholder="请输入站点编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distCode">
             <j-area-linkage type="cascader" v-model="model.distCode" placeholder="请输入省市区"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检测车道编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="laneNumber">
              <a-input v-model="model.laneNumber" placeholder="请输入检测车道编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="称重检测设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipCode">
              <a-input v-model="model.equipCode" placeholder="请输入称重检测设备编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检测数据类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkType">
              <j-dict-select-tag type="list" v-model="model.checkType" dictCode="check_type" placeholder="请选择检测数据类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="检测时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkTime">
              <j-date placeholder="请选择检测时间"  v-model="model.checkTime" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="关联检测单号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="linkCheckNo">
              <a-input v-model="model.linkCheckNo" placeholder="请输入关联检测单号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车辆号牌" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNo">
              <a-input v-model="model.vehicleNo" placeholder="请输入车辆号牌"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateColor">
              <j-dict-select-tag type="list" v-model="model.plateColor" dictCode="plate_color" placeholder="请选择车牌颜色" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车型代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleAxlesType">
              <a-input v-model="model.vehicleAxlesType" placeholder="请输入车型代码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车货总质量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="total">
              <a-input-number v-model="model.total" placeholder="请输入车货总质量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车辆轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
              <a-input-number v-model="model.axles" placeholder="请输入车辆轴数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴重1" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight1">
              <a-input-number v-model="model.weight1" placeholder="请输入轴重1" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴重2" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight2">
              <a-input-number v-model="model.weight2" placeholder="请输入轴重2" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴重3" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight3">
              <a-input-number v-model="model.weight3" placeholder="请输入轴重3" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴重4" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight4">
              <a-input-number v-model="model.weight4" placeholder="请输入轴重4" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴重5" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight5">
              <a-input-number v-model="model.weight5" placeholder="请输入轴重5" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴重6" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight6">
              <a-input-number v-model="model.weight6" placeholder="请输入轴重6" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="轴重7" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="weight7">
              <a-input-number v-model="model.weight7" placeholder="请输入轴重7" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="入口车速" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="speed">
              <a-input-number v-model="model.speed" placeholder="请输入入口车速" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="最大允许总质量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitWeight">
              <a-input-number v-model="model.limitWeight" placeholder="请输入最大允许总质量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="超限量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overWeight">
              <a-input-number v-model="model.overWeight" placeholder="请输入超限量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="超限超载率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overRate">
              <a-input-number v-model="model.overRate" placeholder="请输入超限超载率" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="卸载重量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unloadWeight">
              <a-input-number v-model="model.unloadWeight" placeholder="请输入卸载重量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车货总长度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totalLength">
              <a-input-number v-model="model.totalLength" placeholder="请输入车货总长度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车货总宽度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totalWidth">
              <a-input-number v-model="model.totalWidth" placeholder="请输入车货总宽度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车货总高度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totalHeight">
              <a-input-number v-model="model.totalHeight" placeholder="请输入车货总高度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="超长量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overLength">
              <a-input-number v-model="model.overLength" placeholder="请输入超长量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="超宽量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overWidth">
              <a-input-number v-model="model.overWidth" placeholder="请输入超宽量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="超高量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overHeight">
              <a-input-number v-model="model.overHeight" placeholder="请输入超高量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="是否为大件运输车辆" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isBulkVehicle">
              <j-dict-select-tag type="radio" v-model="model.isBulkVehicle" dictCode="is_bulk_vehicle" placeholder="请选择是否为大件运输车辆" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="匹配的大件运输许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licNo">
              <a-input v-model="model.licNo" placeholder="请输入匹配的大件运输许可证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="车辆名单类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleListType">
              <j-dict-select-tag type="list" v-model="model.vehicleListType" dictCode="vehicle_list_type" placeholder="请选择车辆名单类型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="超载类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overType">
              <j-dict-select-tag type="list" v-model="model.overType" dictCode="" placeholder="请选择超载类型" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'CheckEnterpriseDataOverloadForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/truck/checkEnterpriseDataOverload/add",
          edit: "/truck/checkEnterpriseDataOverload/edit",
          queryById: "/truck/checkEnterpriseDataOverload/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>