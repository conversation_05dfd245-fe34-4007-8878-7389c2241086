<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <j-dict-select-tag type="list" v-model="model.siteCode" dictCode="" placeholder="请选择站点编码" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distCode">
              <a-input v-model="model.distCode" placeholder="请输入行政区划"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="过车数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkNum">
              <a-input-number v-model="model.checkNum" placeholder="请输入过车数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="初检总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="firstCheck">
              <a-input-number v-model="model.firstCheck" placeholder="请输入初检总数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="复检总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recheck">
              <a-input-number v-model="model.recheck" placeholder="请输入复检总数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="动态磅数量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dynamicPound">
              <a-input-number v-model="model.dynamicPound" placeholder="请输入动态磅数量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="静态磅数量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="staticPound">
              <a-input-number v-model="model.staticPound" placeholder="请输入静态磅数量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="大于一吨" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overGtOne">
              <a-input-number v-model="model.overGtOne" placeholder="请输入大于一吨" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="小于一吨" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overLtOne">
              <a-input-number v-model="model.overLtOne" placeholder="请输入小于一吨" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="超载数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overNum">
              <a-input-number v-model="model.overNum" placeholder="请输入超载数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="上传总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="upload">
              <a-input-number v-model="model.upload" placeholder="请输入上传总数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'StatisticsSiteDataStatusForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           siteCode: [
              { required: true, message: '请输入站点编码!'},
           ],
           distCode: [
              { required: true, message: '请输入行政区划!'},
           ],
           checkNum: [
              { required: true, message: '请输入过车数!'},
           ],
           firstCheck: [
              { required: true, message: '请输入初检总数!'},
           ],
           recheck: [
              { required: true, message: '请输入复检总数!'},
           ],
           dynamicPound: [
              { required: true, message: '请输入动态磅数量!'},
           ],
           staticPound: [
              { required: true, message: '请输入静态磅数量!'},
           ],
           overGtOne: [
              { required: true, message: '请输入大于一吨!'},
           ],
           overLtOne: [
              { required: true, message: '请输入小于一吨!'},
           ],
           overNum: [
              { required: true, message: '请输入超载数!'},
           ],
           upload: [
              { required: true, message: '请输入上传总数!'},
           ],
        },
        url: {
          add: "/statistics/statisticsSiteDataStatus/add",
          edit: "/statistics/statisticsSiteDataStatus/edit",
          queryById: "/statistics/statisticsSiteDataStatus/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>