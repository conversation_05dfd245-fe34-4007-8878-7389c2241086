<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业编码">
              <a-input placeholder="请输入企业编码" v-model="queryParam.enterpriseCode"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="检测单号">
              <a-input placeholder="请输入检测单号" v-model="queryParam.checkNo"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="车头车牌号">
                <a-input placeholder="请输入车头车牌号" v-model="queryParam.vehicle"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="轴数">
                <a-input placeholder="请输入轴数" v-model="queryParam.axles"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="超载标识">
                <a-input placeholder="请输入超载标识" v-model="queryParam.overFlag"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('称重数据表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <over-weight-data-modal ref="modalForm" @ok="modalFormOk"></over-weight-data-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import OverWeightDataModal from './modules/OverWeightDataModal'

  export default {
    name: 'OverWeightDataList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      OverWeightDataModal
    },
    data () {
      return {
        description: '称重数据表管理页面',
        // 表头
        columns: [
          {
            title:'企业名称',
            align:"center",
            dataIndex: 'enterpriseCode'
          },
          {
            title:'行政区划',
            align:"center",
            dataIndex: 'checkNo'
          },
          {
            title:'检测时间',
            align:"center",
            dataIndex: 'equipCode'
          },
          {
            title:'称重方向',
            align:"center",
            dataIndex: 'vehicle'
          },
          {
            title:'车牌号',
            align:"center",
            dataIndex: 'color'
          },
          {
            title:'轴数',
            align:"center",
            dataIndex: 'tailVehicle'
          },
          {
            title:'轴型',
            align:"center",
            dataIndex: 'tailColor'
          },
          {
            title:'限重',
            align:"center",
            dataIndex: 'landId'
          },
          {
            title:'重量',
            align:"center",
            dataIndex: 'axles'
          },
          {
            title:'超载数',
            align:"center",
            dataIndex: 'axleType'
          },
          {
            title:'超载率',
            align:"center",
            dataIndex: 'vehicleType'
          },
        ],
        url: {
          list: "/overweight/overWeightData/list",
          delete: "/overweight/overWeightData/delete",
          deleteBatch: "/overweight/overWeightData/deleteBatch",
          exportXlsUrl: "/overweight/overWeightData/exportXls",
          importExcelUrl: "overweight/overWeightData/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'enterpriseCode',text:'企业编码',dictCode:''})
        fieldList.push({type:'string',value:'checkNo',text:'检测单号',dictCode:''})
        fieldList.push({type:'string',value:'equipCode',text:'设别编号',dictCode:''})
        fieldList.push({type:'string',value:'vehicle',text:'车头车牌号',dictCode:''})
        fieldList.push({type:'string',value:'color',text:'车头车牌颜色',dictCode:''})
        fieldList.push({type:'string',value:'tailVehicle',text:'车尾车牌号',dictCode:''})
        fieldList.push({type:'string',value:'tailColor',text:'车尾车牌颜色',dictCode:''})
        fieldList.push({type:'string',value:'landId',text:'车道号',dictCode:''})
        fieldList.push({type:'string',value:'axles',text:'轴数',dictCode:''})
        fieldList.push({type:'string',value:'axleType',text:'轴型',dictCode:''})
        fieldList.push({type:'string',value:'vehicleType',text:'车型',dictCode:''})
        fieldList.push({type:'string',value:'limitWeight',text:'限重',dictCode:''})
        fieldList.push({type:'string',value:'total',text:'总重',dictCode:''})
        fieldList.push({type:'string',value:'overWeight',text:'超重',dictCode:''})
        fieldList.push({type:'string',value:'overRate',text:'超载率',dictCode:''})
        fieldList.push({type:'string',value:'overFlag',text:'超载标识',dictCode:''})
        fieldList.push({type:'string',value:'direction',text:'出/入厂称重',dictCode:''})
        fieldList.push({type:'string',value:'headImage',text:'车头照片',dictCode:''})
        fieldList.push({type:'string',value:'tailImage',text:'车尾照片',dictCode:''})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>