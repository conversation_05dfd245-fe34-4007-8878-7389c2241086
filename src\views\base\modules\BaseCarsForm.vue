<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="车牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNo">
              <a-input v-model="model.vehicleNo" placeholder="请输入车牌号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateColor">
              <j-dict-select-tag type="list" v-model="model.plateColor" dictCode="plate_color" placeholder="请选择车牌颜色" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
              <j-dict-select-tag type="list" v-model="model.axles" dictCode="axles" placeholder="请选择轴数" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="轴型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleAxlesType">
              <a-input v-model="model.vehicleAxlesType" placeholder="请输入轴型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="锁定" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isLock">
              <j-dict-select-tag type="list" v-model="model.isLock" dictCode="is_lock" placeholder="请选择锁定" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'BaseCarsForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           vehicleNo: [
              { required: true, message: '请输入车牌号!'},
           ],
           plateColor: [
              { required: true, message: '请输入车牌颜色!'},
           ],
           axles: [
              { required: true, message: '请输入轴数!'},
           ],
           vehicleAxlesType: [
              { required: true, message: '请输入轴型!'},
           ],
           isLock: [
              { required: true, message: '请输入锁定!'},
           ],
        },
        url: {
          add: "/system/baseCars/add",
          edit: "/system/baseCars/edit",
          queryById: "/system/baseCars/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>