<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <base-unattended-point-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></base-unattended-point-form>
  </j-modal>
</template>

<script>

  import BaseUnattendedPointForm from './BaseUnattendedPointForm'
  export default {
    name: 'BaseUnattendedPointModal',
    components: {
      BaseUnattendedPointForm
    },
    data () {
      return {
        title:'',
        width:800,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
       add (siteCode) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add(siteCode);
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>