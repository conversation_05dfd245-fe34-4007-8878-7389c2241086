<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="车牌号">
              <a-input placeholder="请输入车牌号" v-model="queryParam.vehicle"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="状态">
              <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.status" dictCode="blacklist_status" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="入库状态">
              <j-dict-select-tag placeholder="请选择入库状态" v-model="queryParam.addFlag" dictCode="blacklist_add_flag" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!-- <a-button type="primary" icon="download" @click="handleExportXls('黑名单列表')">导出</a-button> -->
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <!-- <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query> -->
      <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown> -->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
        selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <template slot="blackOverTag" slot-scope="text, record">
          <a-tag color="green" v-if="record.status == 0">{{ text }}</a-tag>
          <a-tag color="yellow" v-else-if="record.status == 1">{{ text }}</a-tag>
          <a-tag color="red" v-else-if="record.status == 2">{{ text }}</a-tag>
          <a-tag color="grey" v-else-if="record.status == 9">{{ text }}</a-tag>
        </template>
        <template slot="blackAddTag" slot-scope="text, record">
          <a-tag color="red" v-if="record.addFlag == 0">{{ text }}</a-tag>
          <a-tag color="green" v-else>{{ text }}</a-tag>
        </template>

        <span slot="action" slot-scope="text, record">

          <a @click="handleDetail(record)">查看车辆超载详情</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleEdit(record)">编辑</a>
              </a-menu-item>
              <!-- <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item> -->
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除黑名单</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <blacklist-car-modal ref="modalForm" @ok="modalFormOk"></blacklist-car-modal>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import BlacklistCarModal from './modules/BlacklistCarModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'

export default {
  name: 'BlacklistCarList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    BlacklistCarModal
  },
  data() {
    return {
      description: '黑名单列表管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function (t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '车牌号',
          align: "center",
          dataIndex: 'vehicle'
        },
        {
          title: '状态',
          align: "center",
          dataIndex: 'status_dictText',
          scopedSlots: { customRender: 'blackOverTag' },
        },
        {
          title: '添加方式',
          align: "center",
          dataIndex: 'addFlag_dictText',
          scopedSlots: { customRender: 'blackAddTag' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          fixed: "right",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: "/blacklist/blacklistCar/list",
        delete: "/blacklist/blacklistCar/delete",
        deleteBatch: "/blacklist/blacklistCar/deleteBatch",
        exportXlsUrl: "/blacklist/blacklistCar/exportXls",
        importExcelUrl: "blacklist/blacklistCar/importExcel",

      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList();
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    initDictConfig() {
    },
    getSuperFieldList() {
      let fieldList = [];
      fieldList.push({ type: 'string', value: 'vehicle', text: '车牌号', dictCode: '' })
      fieldList.push({ type: 'int', value: 'status', text: '状态', dictCode: 'blacklist_status' })
      fieldList.push({ type: 'int', value: 'add_flag', text: '入库方式', dictCode: 'blacklist_add_flag' })
      this.superFieldList = fieldList
    },
    handleDetail: function (record) {
      this.$refs.modalForm.showInfo(record);
      this.$refs.modalForm.title = "近一年超载情况";
      this.$refs.modalForm.disableSubmit = true;
    },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>