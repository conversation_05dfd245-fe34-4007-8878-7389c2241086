<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业编码">
              <a-input placeholder="请输入企业编码" v-model="queryParam.enterpriseCode"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="门禁编码">
              <a-input placeholder="请输入门禁编码" v-model="queryParam.doorCode"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="门禁抓拍编码">
                <a-input placeholder="请输入门禁抓拍编码" v-model="queryParam.doorCameraCode"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="车牌号">
                <a-input placeholder="请输入车牌号" v-model="queryParam.vehicleNo"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="入厂时间">
                <j-date
                  :show-time="true"
                  date-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择开始时间"
                  class="query-group-cust"
                  v-model="queryParam.entryTime_begin"
                ></j-date>
                <span class="query-group-split-cust"></span>
                <j-date
                  :show-time="true"
                  date-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择结束时间"
                  class="query-group-cust"
                  v-model="queryParam.entryTime_end"
                ></j-date>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('企业入口过车表')">导出</a-button>
      <!-- 高级查询区域 -->
      <j-super-query
        :fieldList="superFieldList"
        ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <template slot="plateSlot" slot-scope="text, record">
          <plate :plate="text" :color="Number(record.plateColor)"></plate>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
        </span>
      </a-table>
    </div>

    <check-enterprise-door-entry-modal ref="modalForm" @ok="modalFormOk"></check-enterprise-door-entry-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CheckEnterpriseDoorEntryModal from './modules/CheckEnterpriseDoorEntryModal'
import Plate from '@/components/plate/Plate'

export default {
  name: 'CheckEnterpriseDoorEntryList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CheckEnterpriseDoorEntryModal,
    Plate,
  },
  data() {
    return {
      description: '企业入口过车表管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '企业名称',
          align: 'center',
          dataIndex: 'enterpriseName',
        },
        {
          title: '企业编码',
          align: 'center',
          dataIndex: 'enterpriseCode',
        },

        {
          title: '门禁编码',
          align: 'center',
          dataIndex: 'doorCode',
        },
        {
          title: '车辆图片',
          align: 'center',
          dataIndex: 'checkEnterpriseDoorImagesList',
          customRender: (text) => {
            const dataSource = text || []
            // 使用 Vue 的 createElement (h) 构建虚拟节点
            console.log( dataSource.length > 0,' dataSource.length > 0' ,dataSource,'==>data')
            return this.$createElement('div', [
            dataSource.length == 0
                ? this.$createElement(
                    'span',
                    {
                      style: { fontSize: '12px', fontStyle: 'italic' },
                    },
                    '无图片'
                  )
                : this.$createElement('vue-preview', {
                    attrs: {
                      slides: [
                        {
                          w: 1200, //设置以大图浏览时的宽度
                          h: 800, //设置以大图浏览时的高度
                          src: this.getImgView(dataSource[0].dataUrl),
                          msrc: this.getImgView(dataSource[0].dataUrl),
                        },
                      ],
                    },
                    class: 'preview',
                  }),
            ])
          },
        },
        {
          title: '门禁抓拍编码',
          align: 'center',
          dataIndex: 'doorCameraCode',
        },
        {
          title: '车牌号',
          align: 'center',
          dataIndex: 'vehicleNo',
          scopedSlots: { customRender: 'plateSlot' },
        },
        {
          title: '入厂时间',
          align: 'center',
          sorter: true,
          dataIndex: 'entryTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/enterprise/door/checkEnterpriseDoorEntry/list',
        delete: '/enterprise/door/checkEnterpriseDoorEntry/delete',
        deleteBatch: '/enterprise/door/checkEnterpriseDoorEntry/deleteBatch',
        exportXlsUrl: '/enterprise/door/checkEnterpriseDoorEntry/exportXls',
        importExcelUrl: '/enterprise/door/checkEnterpriseDoorEntry/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'enterpriseName', text: '企业名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'enterpriseCode', text: '企业编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'doorCode', text: '门禁编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'doorCameraCode', text: '门禁抓拍编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicleNo', text: '车牌号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'plateColor', text: '车牌颜色', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'entryTime', text: '入厂时间' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style >
@import '~@assets/less/common.less';

.preview figure img {
	width: 75px;
	height: 45px;
}
</style>
