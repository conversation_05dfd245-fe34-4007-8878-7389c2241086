<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="号牌号码">
              <a-input placeholder="请输入号牌号码" v-model="queryParam.hphm"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="是否有效">
                <j-dict-select-tag placeholder="请选择是否有效" v-model="queryParam.sfyx" dictCode="is_valid"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button type="primary" icon="download" @click="handleExportXls('交警电子抓拍违法车辆处理表')">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown> -->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        <span style="float:right;">
          <a @click="loadData()"><a-icon type="sync" />刷新</a>
          <a-divider type="vertical" />
          <a-popover title="自定义列" trigger="click" placement="leftBottom">
            <template slot="content">
              <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                <a-row style="width: 400px">
                  <template v-for="(item,index) in defColumns">
                    <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span="12"><a-checkbox :value="item.dataIndex"><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox></a-col>
                    </template>
                  </template>
                </a-row>
              </a-checkbox-group>
            </template>
            <a><a-icon type="setting" />设置</a>
          </a-popover>
        </span>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <police-data-illegal-modal ref="modalForm" @ok="modalFormOk"></police-data-illegal-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import Vue from 'vue'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import PoliceDataIllegalModal from './modules/PoliceDataIllegalModal'

  export default {
    name: 'PoliceDataIllegalList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      PoliceDataIllegalModal
    },
    data () {
      return {
        description: '交警电子抓拍违法车辆处理表管理页面',
         //表头
        columns:[],
         //列设置
        settingColumns:[],
        // 表头
        defColumns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'违法序号',
            align:"center",
            dataIndex: 'wfxh'
          },
          {
            title:'电子抓拍设备编号',
            align:"center",
            dataIndex: 'zpsbbh'
          },
          {
            title:'号牌种类',
            align:"center",
            dataIndex: 'hpzl'
          },
          {
            title:'号牌号码',
            align:"center",
            dataIndex: 'hphm'
          },
          {
            title:'车辆类型名称',
            align:"center",
            dataIndex: 'cllxmc'
          },
          {
            title:'发现机关名称',
            align:"center",
            dataIndex: 'fxjgmc'
          },
          {
            title:'机动车所有人',
            align:"center",
            dataIndex: 'jdcsyr'
          },
          {
            title:'联系电话',
            align:"center",
            dataIndex: 'lxdh'
          },
          {
            title:'所有人身份证号',
            align:"center",
            dataIndex: 'syrsfzh'
          },
          {
            title:'违法行为',
            align:"center",
            dataIndex: 'wfxw'
          },
          {
            title:'违法行为描述',
            align:"center",
            dataIndex: 'wfxwms'
          },
          {
            title:'违法地址',
            align:"center",
            dataIndex: 'wfdz'
          },
          {
            title:'违法时间',
            align:"center",
            dataIndex: 'wfsj'
          },
          {
            title:'罚款金额',
            align:"center",
            dataIndex: 'fkje'
          },
          {
            title:' 违法记分数',
            align:"center",
            dataIndex: 'wfjfs'
          },
          {
            title:'是否有效',
            align:"center",
            dataIndex: 'sfyx'
          },
          {
            title:'无效原因描述',
            align:"center",
            dataIndex: 'wxyyms'
          },
          {
            title:'处理标记',
            align:"center",
            dataIndex: 'clbj'
          },
          {
            title:'处理机关名称',
            align:"center",
            dataIndex: 'cljgmc'
          },
          {
            title:'处理时间',
            align:"center",
            dataIndex: 'clsj'
          },
          {
            title:' 当事人',
            align:"center",
            dataIndex: 'dsr'
          },
          {
            title:'当事人身份证号',
            align:"center",
            dataIndex: 'dsrsfzh'
          },
          {
            title:'更新时间',
            align:"center",
            dataIndex: 'gxsj'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/police/policeDataIllegal/list",
          delete: "/police/policeDataIllegal/delete",
          deleteBatch: "/police/policeDataIllegal/deleteBatch",
          exportXlsUrl: "/police/policeDataIllegal/exportXls",
          importExcelUrl: "police/policeDataIllegal/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
      this.initColumns();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'int',value:'wfxh',text:'违法序号',dictCode:''})
        fieldList.push({type:'string',value:'zpsbbh',text:'电子抓拍设备编号',dictCode:''})
        fieldList.push({type:'string',value:'hpzl',text:'号牌种类',dictCode:''})
        fieldList.push({type:'string',value:'hphm',text:'号牌号码',dictCode:''})
        fieldList.push({type:'string',value:'cllxmc',text:'车辆类型名称',dictCode:''})
        fieldList.push({type:'string',value:'fxjgmc',text:'发现机关名称',dictCode:''})
        fieldList.push({type:'string',value:'jdcsyr',text:'机动车所有人',dictCode:''})
        fieldList.push({type:'string',value:'lxdh',text:'联系电话',dictCode:''})
        fieldList.push({type:'string',value:'syrsfzh',text:'所有人身份证号',dictCode:''})
        fieldList.push({type:'string',value:'wfxw',text:'违法行为',dictCode:''})
        fieldList.push({type:'string',value:'wfxwms',text:'违法行为描述',dictCode:''})
        fieldList.push({type:'string',value:'wfdz',text:'违法地址',dictCode:''})
        fieldList.push({type:'datetime',value:'wfsj',text:'违法时间'})
        fieldList.push({type:'BigDecimal',value:'fkje',text:'罚款金额',dictCode:''})
        fieldList.push({type:'int',value:'wfjfs',text:' 违法记分数',dictCode:''})
        fieldList.push({type:'int',value:'sfyx',text:'是否有效',dictCode:''})
        fieldList.push({type:'Text',value:'wxyyms',text:'无效原因描述',dictCode:''})
        fieldList.push({type:'string',value:'clbj',text:'处理标记',dictCode:''})
        fieldList.push({type:'string',value:'cljgmc',text:'处理机关名称',dictCode:''})
        fieldList.push({type:'datetime',value:'clsj',text:'处理时间'})
        fieldList.push({type:'string',value:'dsr',text:' 当事人',dictCode:''})
        fieldList.push({type:'string',value:'dsrsfzh',text:'当事人身份证号',dictCode:''})
        fieldList.push({type:'datetime',value:'gxsj',text:'更新时间'})
        this.superFieldList = fieldList
      },
      initColumns(){
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');
        var key = this.$route.name+":colsettings";
        let colSettings= Vue.ls.get(key);
        if(colSettings==null||colSettings==undefined){
          let allSettingColumns = [];
          this.defColumns.forEach(function (item,i,array ) {
            allSettingColumns.push(item.dataIndex);
          })
          this.settingColumns = allSettingColumns;
          this.columns = this.defColumns;
        }else{
          this.settingColumns = colSettings;
          const cols = this.defColumns.filter(item => {
            if(item.key =='rowIndex'|| item.dataIndex=='action'){
              return true;
            }
            if (colSettings.includes(item.dataIndex)) {
              return true;
            }
            return false;
          })
          this.columns =  cols;
        }
      },
      //列设置更改事件
      onColSettingsChange (checkedValues) {
        var key = this.$route.name+":colsettings";
        Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
        this.settingColumns = checkedValues;
        const cols = this.defColumns.filter(item => {
          if(item.key =='rowIndex'|| item.dataIndex=='action'){
            return true
          }
          if (this.settingColumns.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns =  cols;
      },
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>