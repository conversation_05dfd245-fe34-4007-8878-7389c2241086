<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    :fullscreen="false"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <case-files-pre-turn-page ref="realForm"></case-files-pre-turn-page>
  </j-modal>
</template>

<script>
import CaseFilesPreTurnPage from './CaseFilesPreTurnPage'
export default {
    name:"CaseListMenuModel",
    components: {
        CaseFilesPreTurnPage
    },
    data () {
        return {
            title:"查看案件文件",
            width: 1000,
            visible: false,
            disableSubmit:false,
        }
    },
    methods: {
        show(record) {
            this.visible = true;
            this.$nextTick(()=>{
                this.$refs.realForm.show(record);
            })
        },
        close () {
            this.$emit('close');
            this.visible = false;
        },
        handleCancel () {
            this.close()
        }
    }


}




</script>