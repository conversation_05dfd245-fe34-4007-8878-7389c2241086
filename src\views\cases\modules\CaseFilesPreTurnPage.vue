<template>
  <a-card title="案件目录列表文件查看" style="min-width: 800px;overflow-x:auto ">
    <a-row>
      <!-- 左侧文件树 -->
      <a-col :span="6">
        <a-tree
          showLine
          :treeData="menusTreeData"
          :expandedKeys="[expandedKeys[0]]"
          :selectedKeys="selectedKeys"
          :style="{'height':'600px','border-right':'2px solid #c1c1c1','overflow-y':'auto'}"
          @expand="onExpand"
          @select="this.onSelect"
        >
        </a-tree>
      </a-col>
      <!--右侧缩略图-->
      <a-col :span="18">
        <a-row style="margin-top: 10px">
           <a-col :span="24" style="padding-left: 2%;">
            <!-- <img :src="imgUrl" preview> -->
            <iframe-page-view :src="fileUrl"></iframe-page-view>
          </a-col>
          <a-col :span="24" style="padding-left: 2%;margin-bottom: 10px">
            <a-button @click="prev" type="primary"><a-icon type="left" />上一页</a-button>
            <a-button @click="next" type="primary" style="margin-left: 8px">下一页<a-icon type="right" /></a-button>
            <span style="margin-left: 15%;font-weight: bolder">{{ navName }}</span>
          </a-col>
         
        </a-row>
      </a-col>
    </a-row>
  </a-card>
</template>

<script>
import draggable from 'vuedraggable'
import IframePageView from '../../../components/layouts/IframePageView';
import { showMenusAndFiles } from '@/api/api'
export default{
     name: 'CaseFilesPreTurnPage',
     components: {
         draggable,
         IframePageView
     },
     data(){
         return {
             description:"案件预览",
             menusTreeData:[],
             treeData: [],
             selectedKeys:[],
             expandedKeys:[],
             sort: 0,
             imgUrl: '',
             navName: '',
             imgList: [],
             fileUrl: '',
             fileList: [],
         }
     },
     created() {},
     methods: {
         show(record) {
             var code = record.code;
             var site = record.siteCode;
             showMenusAndFiles({site:site,code:code}).then((res)=> {
                 if(res.success) {
                     this.parseCaseMenuData(res.result);
                 }
             });
         },
         parseCaseMenuData(menuList) {
           console.log(menuList);
            var count = 0;
            for(var i=0;i<menuList.length;i++)
            {
                var files = [];
                for(var j=0; j < menuList[i].caseFiles.length; j++)
                {
                    files[j] = {
                        title: j + 1 + '页',
                        key: menuList[i].caseCode + '-' + j,
                        fileUrl: window._CONFIG.VUE_APP_ONLINE_BASE_URL + encodeURIComponent(menuList[i].caseFiles[j].docUrl),
                    };
                }
                this.menusTreeData[i] = {
                    title: menuList[i].menuName,
                    key: menuList[i].menuCode,
                    children: files
                }
            }
            this.getFileList();
         },
         getFileList(){
            var count = 0;
            for(var i=0; i < this.menusTreeData.length; i++)
            {
                for(var j=0; j < this.menusTreeData[i].children.length; j++)
                {
                    this.fileList.push({
                        key:this.menusTreeData[i].children[j].key,
                        pkey:this.menusTreeData[i].key,
                        sort:count++,
                        navName:this.menusTreeData[i].title+"/"+this.menusTreeData[i].children[j].title,
                        fileUrl: this.menusTreeData[i].children[j].fileUrl
                    });
                }
            }
            this.setValue(this.fileList[this.sort]);
         },
         onSelect(selectedKeys, info){
            for(var i=0;i<this.fileList.length;i++){
                if(this.fileList[i].key === selectedKeys[0]){
                    this.sort = this.fileList[i].sort;
                    this.setValue(this.fileList[i]);
                    break;
                }
            }
         },
         onExpand(expandedKeys){
            this.expandedKeys = [];
            if(expandedKeys !== null && expandedKeys !== ''){
                this.expandedKeys[0] = expandedKeys[1];
            }    
         },
         prev() {
             if(this.sort === 0){
                this.sort = this.fileList.length-1;
             }else{
                this.sort = this.sort - 1;
             }
            this.setValue(this.fileList[this.sort]);
         },
         next() {
            if(this.sort === this.fileList.length-1){
                this.sort = 0;
              }else{
                this.sort = this.sort + 1;
              }
            this.setValue(this.fileList[this.sort]);
         },
         setValue(value) {
            this.selectedKeys = [];
            this.fileUrl = value.fileUrl ;
            this.selectedKeys[0] = value.key;
            this.expandedKeys[0] = value.pkey;
            this.navName = value.navName;
         }

     }


}
</script>