<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-form-item label="站点编码">
               <j-input placeholder="请输入站点编码" v-model="queryParam.code"></j-input>
            </a-form-item>
          </a-col>
          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-form-item label="站点名称">
              <j-input placeholder="请输入站点名称" v-model="queryParam.name"></j-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="行政区划">
                <j-area-linkage type="cascader" v-model="queryParam.distCode" placeholder="请选择省市区"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('无人站点表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        <span style="float:right;">
          <a @click="loadData()"><a-icon type="sync" />刷新</a>
          <a-divider type="vertical" />
          <a-popover title="自定义列" trigger="click" placement="leftBottom">
            <template slot="content">
              <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                <a-row style="width: 400px">
                  <template v-for="(item,index) in defColumns">
                    <template v-if="item.key!='rowIndex'&& item.dataIndex!='action'">
                        <a-col :span="12"><a-checkbox :value="item.dataIndex"><j-ellipsis :value="item.title" :length="10"></j-ellipsis></a-checkbox></a-col>
                    </template>
                  </template>
                </a-row>
              </a-checkbox-group>
            </template>
            <a><a-icon type="setting" />设置</a>
          </a-popover>
        </span>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="code"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :expandedRowKeys= "expandedRowKeys"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @expand="handleExpand"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="pcaSlot" slot-scope="text">
          <div>{{ getPcaText(text) }}</div>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <template slot="customRenderStatus" slot-scope="status">
          <a-tag v-if="status==1" color="green">正常</a-tag>
          <a-tag v-if="status==0" color="red">异常</a-tag>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item> 
                <a @click="handlePoint(record)" >添加检测点</a>
              </a-menu-item>
              <a-menu-item> 
                <a @click="uploadProvincialPlatform(record)" >上报省平台</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      <a-table
          slot="expandedRowRender"
          :columns="innerColumns"
          :dataSource="innerData"
          size="middle"
          bordered
          rowKey="id"
          :pagination="false"
          :loading="loading"
          >
       
        <template slot="value" slot-scope="text, record">
          {{ text }} %
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="handlePointEdit(record)" >编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handlePointDelete(record.id)" >
              <a>删除</a>
          </a-popconfirm>
        </span>
       </a-table>

      </a-table>
    </div>

    <base-unattended-modal ref="modalForm" @ok="modalFormOk"></base-unattended-modal>
    <base-unattended-point-modal ref="unattendedPointForm" @ok="unattendedFormOk()"></base-unattended-point-modal>

  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import Vue from 'vue'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseUnattendedModal from './modules/BaseUnattendedModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import Area from '@/components/_util/Area'
  import { getAction,deleteAction } from '@/api/manage'
  import BaseUnattendedPointModal from "./modules/BaseUnattendedPointModal.Style#Drawer"

  export default {
    name: 'BaseUnattendedList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      BaseUnattendedModal,
      BaseUnattendedPointModal
    },
    data () {
      return {
        description: '无人站点表管理页面',
        siteCode:'',
        isorter:{
          column: 'create_time',  //修改默认排序
          order: 'asc',
        },
        // 表头
        columns: [],
        //列设置
        settingColumns:[],
        // 表头
        defColumns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'站点编码',
            align:"center",
            dataIndex: 'code'
          },
          {
            title:'站点名称',
            align:"center",
            dataIndex: 'name'
          },
          {
            title:'站点简称',
            align:"center",
            dataIndex: 'uname'
          },
          {
            title:'检测车道数',
            align:"center",
            dataIndex: 'jccds'
          },
          {
            title:'行政区划',
            align:"center",
            dataIndex: 'distCode',
            scopedSlots: {customRender: 'pcaSlot'}
          },
          {
            title:'站点状态',
            align:"center",
            dataIndex: 'siteZt_dictText'
          },
          {
            title:'网络状态',
            align:"center",
            dataIndex: 'netStatus',
            scopedSlots: { customRender: 'customRenderStatus' },
            filters: [
              { text: '正常', value: '1' },
              { text: '异常', value: '0' },
            ]
          },
          {
            title:'数据状态',
            align:"center",
            dataIndex: 'dataStatus',
            scopedSlots: { customRender: 'customRenderStatus' },
            filters: [
              { text: '正常', value: '1' },
              { text: '异常', value: '0' },
            ]
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        //子表
        expandedRowKeys:[],
        innerData:[],
        innerColumns:[
          {
            title:'车道号',
            align:"center",
            dataIndex: 'lane'
          },
          {
            title:'车道方向',
            align:"center",
            dataIndex: 'direction'
          },
          {
            title:'磅型',
            align:"center",
            dataIndex: 'poundType_dictText'
          },
          {
            title:'超载率',
            align:"center",
            dataIndex: 'overRate',
            customRender:function (t,r,index) {
              return t + "%";
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/system/baseUnattended/list",
          delete: "/system/baseUnattended/delete",
          deleteBatch: "/system/baseUnattended/deleteBatch",
          exportXlsUrl: "/system/baseUnattended/exportXls",
          importExcelUrl: "system/baseUnattended/importExcel",
          listPointAll: "/system/baseUnattendedPoint/listAll",
          deletePoint: "/system/baseUnattendedPoint/delete",
        },
        dictOptions:{},
        pcaData:'',
        superFieldList:[],
      }
    },
    created() {
      this.pcaData = new Area()
      this.getSuperFieldList();
      this.initColumns();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      getPcaText(code){
        return this.pcaData.getText(code);
      },
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'code',text:'站点编码',dictCode:''})
        fieldList.push({type:'string',value:'name',text:'站点名称',dictCode:''})
        fieldList.push({type:'string',value:'uname',text:'站点简称',dictCode:''})
        fieldList.push({type:'int',value:'jccds',text:'检测车道数',dictCode:''})
        fieldList.push({type:'pca',value:'distCode',text:'行政区划'})
        fieldList.push({type:'int',value:'siteZt',text:'站点状态',dictCode:'siteZt'})
        fieldList.push({type:'int',value:'netStatus',text:'网络状态',dictCode:''})
        fieldList.push({type:'int',value:'dataStatus',text:'数据状态',dictCode:''})
        fieldList.push({type:'string',value:'ip',text:'IP地址',dictCode:''})
        this.superFieldList = fieldList
      },
      onColSettingsChange (checkedValues){
          var key = this.$route.name+":colsettings";
          Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
          this.settingColumns = checkedValues;
          const cols = this.defColumns.filter(item => {
            if(item.key =='rowIndex'|| item.dataIndex=='action'){
              return true
            }
            if (this.settingColumns.includes(item.dataIndex)) {
              return true
            }
            return false
          })
          this.columns =  cols;
      },
      initColumns(){
        //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
        //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');
        var key = this.$route.name+":colsettings";
        let colSettings= Vue.ls.get(key);
        if(colSettings==null||colSettings==undefined){
          let allSettingColumns = [];
          this.defColumns.forEach(function (item,i,array ) {
            allSettingColumns.push(item.dataIndex);
          })
          this.settingColumns = allSettingColumns;
          this.columns = this.defColumns;
        }else{
          this.settingColumns = colSettings;
          const cols = this.defColumns.filter(item => {
            if(item.key =='rowIndex'|| item.dataIndex=='action'){
              return true;
            }
            if (colSettings.includes(item.dataIndex)) {
              return true;
            }
            return false;
          })
          this.columns =  cols;
        }
      },
      //打开子表
      handleExpand(expanded, record){
        this.expandedRowKeys=[];
        this.innerData=[];
        if(expanded===true){
          this.loading = true;
          this.expandedRowKeys.push(record.code);
          this.siteCode = record.code;
          getAction(this.url.listPointAll, {code: record.code}).then((res) => {
            if (res.success) {
              this.loading = false;
              this.innerData = res.result.records;
            }
          });
        }
      },
      //添加检测点
      handlePoint(record){
        this.$refs.unattendedPointForm.add(record.code);
      },
      unattendedFormOk() {
        // 新增/修改 成功时，重载列表
        this.initData(this.siteCode);
      },
      initData(siteCode) {
          if(!this.url.listPointAll){
            this.$message.error("请设置url.listPointAll属性!")
            return
          }
          this.loading = true;
          getAction(this.url.listPointAll, {code: siteCode}).then((res) => {
            if (res.success) {
              this.innerData = res.result.records;
            }
            if(res.code===510){
              this.$message.warning(res.message)
            }
            this.loading = false;
          })
      },
      //编辑子表内容
      handlePointEdit(record){
        this.$refs.unattendedPointForm.edit(record);
      },
      // 删除子表
      handlePointDelete(id){
          if(!this.url.deletePoint){
            this.$message.error("请设置url.deletePoint属性!")
            return
          }
          var that = this;
          deleteAction(that.url.deletePoint, {id: id}).then((res) => {
            if (res.success) {
              //重新计算分页问题
              //that.reCalculatePage(1)
              that.$message.success(res.message);
              this.initData(this.siteCode);
            } else {
              that.$message.warning(res.message);
            }
          });
      },
       //筛选需要重写handleTableChange
      handleTableChange(pagination, filters, sorter) {
        //分页、排序、筛选变化时触发
        //TODO 筛选
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field;
          this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
        }
        console.log(filters);
        //这种筛选方式只支持单选
        if(filters.dataStatus)
        {
          this.filters.dataStatus = filters.dataStatus[0];
        }
        if(filters.netStatus)
        {
          this.filters.netStatus = filters.netStatus[0];
        }
        
       
        this.ipagination = pagination;
        this.loadData();
      },

    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }
  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>