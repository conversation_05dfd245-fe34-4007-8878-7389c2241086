<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
             <a-col :span="12">
                <a-form-model-item label="移交单位" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="handoverDepartment">
                  <a-input v-model="model.handoverDepartment" placeholder="请输入移交单位"  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="货物" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="goodsCode">
                  <j-dict-select-tag placeholder="请选择货物类型" v-model="model.goodsCode" dictCode="base_goods,name,code"/>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="是否要情" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="severe">
                  <j-dict-select-tag type="list" v-model="model.severe" dictCode="is_severe" placeholder="请选择是否达到要情报告单" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item label="货物详情" :wrapperCol="{ span: 22, offset:1}" prop="goodsDetail">
                  <a-textarea v-model="model.goodsDetail" placeholder="请输入具体货物" :auto-size="{ minRows: 3, maxRows: 8 }"/>
                </a-form-model-item>
              </a-col>
               <a-col :span="24">
                <a-form-model-item label="备注" :wrapperCol="{ span: 22, offset:1}" prop="remark">
                  <a-textarea v-model="model.remark" placeholder="请输入备注" :auto-size="{ minRows: 3, maxRows: 8 }"/>
                </a-form-model-item>
              </a-col>
        </a-row>
       
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
  import { httpAction, getAction,postAction } from '@/api/manage'
  import JThirdAppDropdown from '@/components/jeecgbiz/thirdApp/JThirdAppDropdown'
  export default {
    name: 'CheckSiteDataOverloadAddForm',
    components: {
        JThirdAppDropdown,
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        isDisabled:true,
        isshow:false,
        validatorRules:{
          siteCode: [
              { required: true, message: '请输入站点编码!'},
          ],
        },
        model:{
          
        },
        submitModel:{},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        url: {
          added: "/truck/checkSiteDataOverload/added",
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
      userInfo() {
        return this.$store.getters.userInfo
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add(record){
        this.model = Object.assign({}, record);
        this.model.severe = "0"
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            this.submitModel.id = this.model.id;
            this.submitModel.handoverDepartment = this.model.handoverDepartment;
            this.submitModel.goodsCode = this.model.goodsCode;
            this.submitModel.goodsDetail = this.model.goodsDetail;
            this.submitModel.remark = this.model.remark;
            this.submitModel.severe = this.model.severe;
            httpAction(this.url.added,this.submitModel,'put').then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
   
    }
  }
</script>