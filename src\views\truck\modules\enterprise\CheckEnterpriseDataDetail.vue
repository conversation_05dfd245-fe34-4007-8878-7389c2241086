<template>
  <a-spin :spinning="confirmLoading" :model="model">
      <a-col :span="24">
        <template>
            <div v-for="(fileDetail,index) in this.model.images" :key="index">
                  <div style="float: left;width:175px;height:175px;margin-right: 10px;margin: 0 8px 8px 0; text-align:center">
                    <div
                      style="width: 100%;height: 100%;position: relative;padding: 8px;border: 1px solid #d9d9d9;border-radius: 4px;">
                      <img style="width: 100%;" :src="fileDetail.dataUrl" preview="0" :onerror="defaultImg">
                    </div>
                     <span style="text-aligin:center">{{ fileDetail.fileName }}</span>
                  </div>
                </div>
        </template>
      </a-col>
      <a-col :span="24">
        <a-card>
            <detail-list title="车辆信息">
                <detail-list-item term="检测站点"><j-ellipsis :value="this.model.siteName" :length="10"/></detail-list-item>
                <detail-list-item term="行政区划">{{ this.getPcaText(this.model.distCode) }}</detail-list-item>
                <detail-list-item term="检测类型">{{ this.model.checkType_dictText }}</detail-list-item>
                <detail-list-item term="车道号">{{ this.model.laneNumber }}</detail-list-item>
                <!-- <detail-list-item term="车道方向">{{this.model.baseSitePoint.direction }}</detail-list-item> -->
                <detail-list-item term="车牌号">{{ this.model.vehicleNo }}</detail-list-item>
                <detail-list-item term="车牌颜色">{{ this.model.plateColor_dictText }}</detail-list-item>
                <detail-list-item term="检测时间">{{ this.model.checkTime }}</detail-list-item>
                <detail-list-item term="轴数">{{ this.model.axles }}</detail-list-item>
                <detail-list-item term="轴型">{{ this.model.vehicleAxlesType  }}</detail-list-item>
                <detail-list-item term="实重">{{ this.model.total  }}</detail-list-item>
                <detail-list-item term="限重">{{  this.model.limitWeight }}</detail-list-item>
                <div v-if="this.model.isOverload == 1">
                    <detail-list-item term="超重">{{ this.model.overWeight  }}</detail-list-item>
                    <detail-list-item term="超载率">{{ this.model.overRate  }}</detail-list-item>
                </div>
                <detail-list-item term="车辆类型" >{{ this.model.vehicleListType_dictText }}</detail-list-item>
                <detail-list-item term="是否大件" >{{ this.model.isBulkVehicle_dictText }}</detail-list-item>
                <detail-list-item term="轴一重量" >{{ this.model.weight1 }}</detail-list-item>
                <detail-list-item term="轴二重量" >{{ this.model.weight2  }}</detail-list-item>
                <detail-list-item term="轴三重量" >{{ this.model.weight3 }}</detail-list-item>
                <detail-list-item term="轴四重量" >{{ this.model.weight4 }}</detail-list-item>
                <detail-list-item term="轴五重量" >{{ this.model.weight5 }}</detail-list-item>
                <detail-list-item term="轴六重量" >{{ this.model.weight6  }}</detail-list-item>
                <detail-list-item term="其他轴重" >{{ this.model.weight7 }}</detail-list-item>
            </detail-list>
        </a-card>
      </a-col>
      <a-col :span="24" style="padding-top: 20px;" v-if='relatedShow'>
        <a-card :title="relatedTitle">
          <a-table :columns="relatedDataColumns" :dataSource="relatedData" :row-key="record => record.id" :scroll="{ x: 1300 }" :loading="loading"/>
        </a-card>
      </a-col>
  </a-spin>
</template>
<script>
import ARow from 'ant-design-vue/es/grid/Row'
import DetailList from '@/components/tools/DetailList'
import Area from '@/components/_util/Area'
import { queryRelatedEnterpriseData } from '@api/api'


const DetailListItem = DetailList.Item
export default {
  name: 'CheckEnterpriseDataDetail',
  components: {
    ARow,DetailList,DetailListItem
  },
  data() {
    return {
      spinning: false,
      confirmLoading: false,
      model:{
        baseSitePoint:{
          direction:"",
        }
      },
      relatedShow: false,
      loading: false,
      relatedDataColumns: [
        {
          title: '检测类型',
          dataIndex: 'checkType_dictText',
          key: 'checkType_dictText',
        },
        {
          title: '是否超载',
          dataIndex: 'isOverload_dictText',
          key: 'agisOverload_dictTexte',
        },
        {
          title: '检测时间',
          dataIndex: 'checkTime',
          key: 'checkTime',
        },
        {
          title: '轴数',
          key: 'axles',
          dataIndex: 'axles',
        },
        {
          title: '轴型',
          key: 'vehicleAxlesType',
          dataIndex: 'vehicleAxlesType',
        },
        {
          title: '限重',
          key: 'limitWeight',
          dataIndex: 'limitWeight',
        },
        {
          title: '实重',
          key: 'total',
          dataIndex: 'total',
        },
        {
          title: '轴一重量',
          key: 'weight1',
          dataIndex: 'weight1',
        },
        {
          title: '轴二重量',
          key: 'weight2',
          dataIndex: 'weight2',
        },{
          title: '轴三重量',
          key: 'weight3',
          dataIndex: 'weight3',
        },{
          title: '轴四重量',
          key: 'weight4',
          dataIndex: 'weight4',
        },{
          title: '轴五重量',
          key: 'weight5',
          dataIndex: 'weight5',
        },{
          title: '轴六重量',
          key: 'weight6',
          dataIndex: 'weight6',
        },
      ],
      relatedData: [],
      relatedTitle: '',
      dataSource: [{
          key:0,
          fileDetails:[
            {
              imgUrl:"https://static.jeecg.com/upload/test/3a4490d5d1cd495b826e528537a47cc1.jpg"
            },

          ]
        }],
    }
  },
  props:{},
  computed: {
    defaultImg(){
        return 'this.src="'+ require("@assets/logo.png") + '"';
    }
  },
  created(){
    this.$previewRefresh()
    this.pcaData = new Area(this.$Jpcaa)
  },
  methods: {
    //解析行政区划
    getPcaText(code){
      return this.pcaData.getText(code);
    },
    show(record){
      this.model = Object.assign({},record);
      if (record.checkType == '1' && record.isOverload == '1') {
        this.relatedTitle = '复检数据'
        this.relatedShow = true
      } else if (record.checkType == '2') {
        this.relatedTitle = '初检数据'
        this.relatedShow = true
      }
      if (this.relatedShow) {
        this.loading = true
        queryRelatedEnterpriseData(record).then((res) => {
          this.loading = false
          this.relatedData = res.result
        }).catch((e) => {
          this.loading = false
        });
      }
    }
  },
}
</script>
