<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="序号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="xh">
              <a-input v-model="model.xh" placeholder="请输入序号"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="站点编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <j-dict-select-tag placeholder="请选择站点" v-model="model.siteCode" dictCode="base_site,name,code" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="站点简称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="kkmc">
              <a-input v-model="model.kkmc" placeholder="请输入站点简称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="检测方向" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cdmc">
              <a-input v-model="model.cdmc" placeholder="请输入检测方向"></a-input>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="8">
            <a-form-model-item label="设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sbbh">
              <a-input v-model="model.sbbh" placeholder="请输入设备编号"  ></a-input>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-model-item label="车辆分类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="clfl">
              <a-input v-model="model.clfl" placeholder="请输入车辆分类"></a-input>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="8">
            <a-form-model-item label="号牌种类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hpzl">
              <a-input v-model="model.hpzl" placeholder="请输入号牌种类"  ></a-input>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-model-item label="车牌号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hphm">
              <a-input v-model="model.hphm" placeholder="请输入车牌号码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hpys">
              <j-dict-select-tag
                placeholder="请选择车牌颜色"
                v-model="model.hpys"
                dictCode="plate_color"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="违法地行政区划" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="xzqh">
              <j-area-linkage type="cascader" v-model="model.xzqh" placeholder="请选择省市区" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="违法地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfdz">
              <a-input v-model="model.wfdz" placeholder="请输入违法地址"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="违法时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfsj">
              <j-date placeholder="请选择违法时间" v-model="model.wfsj" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%" />
            </a-form-model-item>
          </a-col>
           <a-col :span="12">
            <a-form-model-item label="违法行为代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfxwdm">
              <a-input v-model="model.wfxwdm" placeholder="请输入违法行为代码"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="违法行为" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="wfxw">
              <a-input v-model="model.wfxw" placeholder="请输入违法行为"></a-input>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="8">
            <a-form-model-item label="照片数量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="zpsl">
              <a-input v-model="model.zpsl" placeholder="请输入照片数量"  ></a-input>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="12">
            <a-form-model-item label="车牌图片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tplj">
              <a-input v-model="model.tplj" placeholder="请输入车牌图片"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="速度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="clsd">
              <a-input v-model="model.clsd" placeholder="请输入速度"></a-input>
            </a-form-model-item>
          </a-col>
         
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'

export default {
  name: 'PoliceDataForm',
  components: {
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      model: {
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
      },
      confirmLoading: false,
      validatorRules: {
      },
      url: {
        add: "/police/policeData/add",
        edit: "/police/policeData/edit",
        queryById: "/police/policeData/queryById"
      }
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    },
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model));
  },
  methods: {
    add() {
      this.edit(this.modelDefault);
    },
    edit(record) {
      this.model = Object.assign({}, record);
      this.visible = true;
    },
    submitForm() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          if (!this.model.id) {
            httpurl += this.url.add;
            method = 'post';
          } else {
            httpurl += this.url.edit;
            method = 'put';
          }
          httpAction(httpurl, this.model, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message);
              that.$emit('ok');
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            that.confirmLoading = false;
          })
        }

      })
    },
  }
}
</script>