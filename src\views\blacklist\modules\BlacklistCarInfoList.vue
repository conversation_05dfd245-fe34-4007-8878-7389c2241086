<template>
    <div>
        <a-table :columns="columns" :data-source="dataSource" size="middle" :pagination="false">
            <template slot="title" slot-scope="currentPageData">
                <a-card :bordered="false">
                    <a-row>
                        <a-col :sm="12" :xs="24">
                            <head-info title="称重次数" :content="weightNum" :center="true" :bordered="true" />
                        </a-col>
                        <a-col :sm="12" :xs="24">
                            <head-info title="超载次数" :content="overloadNum" :center="true" :bordered="false"/>
                        </a-col>
                    </a-row>
                </a-card>

            </template>

        </a-table>

    </div>
</template>
  
<script>
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import HeadInfo from '@/components/tools/HeadInfo'
import { httpAction, getAction, postAction } from '@/api/manage'
export default {
    name: "BlacklistCarInfoList",
    props: {
        visible: {
            type: Boolean,
            default: false,
            required: false,
        },

    },
    components: {
        HeadInfo,
        mixins: [JeecgListMixin, mixinDevice],
    },
    data() {
        return {
            description: '近一年车辆超载详情',
            weightNum: "0次",
            overloadNum: "0次",
            columns: [
                {
                    title: '#',
                    dataIndex: '',
                    key: 'rowIndex',
                    width: 60,
                    align: "center",
                    customRender: function (t, r, index) {
                        return parseInt(index) + 1;
                    }
                },
                {
                    title: '车牌号',
                    align: "center",
                    dataIndex: 'vehicleNo'
                },
                {
                    title: '称重时间',
                    align: "center",
                    dataIndex: 'checkTime',
                },
                {
                    title: '称重站点',
                    align: "center",
                    dataIndex: 'siteName',
                },
                {
                    title: '重量',
                    align: "center",
                    dataIndex: 'total',
                },
                {
                    title: '轴型',
                    align: "center",
                    dataIndex: 'vehicleAxlesType',
                },
                {
                    title: '轴数',
                    align: "center",
                    dataIndex: 'axles',
                },
                {
                    title: '超载数',
                    align: "center",
                    dataIndex: 'overWeight',
                },
               
            ],
            dataSource: [],
            url: {
                list: "/blacklist/blacklistCar/getVehicleById",
            },
        }
    },
    created() {

    },
    methods: {
        show(data) {
            console.log(data);
            var params={
                vehicle: data.vehicle
            }
            getAction(this.url.list, params).then((res) => {
                if (res.success) {
                    this.weightNum = res.result.carWeightNum + "次";
                    this.overloadNum = res.result.carOverloadNum + "次";
                    this.dataSource = res.result.carList;
                    //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
                    // this.dataSource = res.result.records || res.result;
                    // if (res.result.total) {
                    //     this.ipagination.total = res.result.total;
                    // } else {
                    //     this.ipagination.total = 0;
                    // }
                    //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.loading = false
            })
        }

    }
}
</script>
  
<style lang="less" scoped>
th.column-money,
td.column-money {
    text-align: right !important;
}
</style>