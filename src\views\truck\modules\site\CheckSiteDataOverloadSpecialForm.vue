<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-divider orientation="left">
          车辆检测信息
        </a-divider>
        <a-row>
          <a-col :span="8">
             <a-form-model-item label="站点名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="siteCode">
              <j-dict-select-tag type="list" v-model="model.siteCode" dictCode="base_site,name,code" placeholder="请选择站点名称"  :disabled="isDisabled"/>
            </a-form-model-item>
          </a-col>
           <a-col :span="8">
             <div id="specialPlateInput">
              <a-form-model-item label="车牌号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleNo">
                <a-input v-model="model.vehicleNo" placeholder="请输入车牌号码"  @focus="getFocus"></a-input>
                 <transition name="info">
                  <div style="width: 350px;height: 175px;background: red;z-index: 20;position: absolute;left:-30px" v-show="isshow">
                      <div id="single-keyboard-box">
                          <mixed-keyboard ref="keyboard" :args="args"
                            :callbacks="callbacks">
                          </mixed-keyboard>
                      </div>
                  </div>
              </transition>
              </a-form-model-item>
              </div>
          </a-col>
          <a-col :span="8">
              <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plateColor">
                <j-dict-select-tag type="list" v-model="model.plateColor" dictCode="plate_color" placeholder="请选择车牌颜色" />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
              <a-form-model-item label="挂车车牌" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hangVehicleNo">
                <a-input v-model="model.hangVehicleNo" placeholder="请输入挂车牌号码"  ></a-input>
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
              <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
                  <j-dict-select-tag type="list" v-model="model.axles" dictCode="axles" placeholder="请选择轴数" />
                </a-form-model-item>
          </a-col>
          <a-col :span="8">
              <a-form-model-item label="车货总重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="total">
                   <a-input v-model="model.total" placeholder="请输入检测的车货总质量"  addon-after="KG"></a-input>
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
              <a-form-model-item label="超载率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overRate">
                  <a-input v-model="model.overRate" placeholder="请输入超载率"  :disabled="isDisabled"  addon-after="%"></a-input>
                </a-form-model-item>
          </a-col>
        </a-row>
        <a-divider orientation="left">
          驾驶员信息
        </a-divider>
        <a-row>
           <a-col :span="12">
             <a-form-model-item label="驾驶员姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverName">
               <a-input v-model="model.driverName" placeholder="请输入驾驶员姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
            <a-col :span="12">
             <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverIdCard">
               <a-input v-model="model.driverIdCard" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
           <a-col :span="12">
             <a-form-model-item label="道路运输从业资格证" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverQuaCard">
               <a-input v-model="model.driverQuaCard" placeholder="请输入道路运输从业资格证号" ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
             <a-form-model-item label="从业发证机关" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="driverQuaRegOrg">
               <a-input v-model="model.driverQuaRegOrg" placeholder="请输入从业资格证发证机关"  ></a-input>
            </a-form-model-item>
          </a-col>
         </a-row>
        <a-divider orientation="left">
          车辆证件信息
        </a-divider>
         <a-row>
         <a-col :span="12">
              <a-form-model-item label="道路运输证" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleQuaCode">
                <a-input v-model="model.vehicleQuaCode" placeholder="请输入道路运输证号"  ></a-input>
              </a-form-model-item>
            </a-col>
          <a-col :span="12">
              <a-form-model-item label="运输发证机关" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleRegOrg">
                <a-input v-model="model.vehicleRegOrg" placeholder="请输入道路运输证发证机关"  ></a-input>
              </a-form-model-item>
          </a-col>
            <a-col :span="12">
              <a-form-model-item label="挂车运输证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hangVehicleQuaCode">
                <a-input v-model="model.hangVehicleQuaCode" placeholder="请输入挂车道路运输证号"  ></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="挂车运输证发证机关" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hangVehicleRegOrg">
                <a-input v-model="model.hangVehicleRegOrg" placeholder="请输入挂车道路运输证发证机关"  ></a-input>
              </a-form-model-item>
            </a-col>
             <a-col :span="12">
              <a-form-model-item label="所属运输企业名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="companyName">
                <a-input v-model="model.companyName" placeholder="请输入所属运输企业名称"  ></a-input>
              </a-form-model-item>
            </a-col>
             <a-col :span="12">
              <a-form-model-item label="运输企业信用代码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="unifiedCreditCode">
                <a-input v-model="model.unifiedCreditCode" placeholder="请输入运输企业社会统一信用代码"  ></a-input>
              </a-form-model-item>
            </a-col>
             <a-col :span="12">
              <a-form-model-item label="许可证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="companyQuaCode">
                <a-input v-model="model.companyQuaCode" placeholder="请输入道路运输经营许可证号"  ></a-input>
              </a-form-model-item>
            </a-col>
             <a-col :span="12">
              <a-form-model-item label="许可证发证机关" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="companyRegOrg">
                <a-input v-model="model.companyRegOrg" placeholder="请输入道路运输经营许可证发证机关"  ></a-input>
              </a-form-model-item>
            </a-col>
        </a-row>
        <a-divider orientation="left">
          特殊车辆信息
        </a-divider>
        <a-row>
          <a-col :span="8">
              <a-form-model-item label="货源地" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distOfStart">
                <j-area-linkage type="cascader" v-model="model.distOfStart" placeholder="请选择省市区"/>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="目的地" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="distOfDestination">
                <j-area-linkage type="cascader" v-model="model.distOfDestination" placeholder="请选择省市区"/>
              </a-form-model-item>
            </a-col>
           <a-col :span="8">
              <a-form-model-item label="特殊车辆类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleType">
                  <j-dict-select-tag type="list" v-model="model.vehicleType" dictCode="vehicle_type" placeholder="请选择特殊车辆类型" />
                </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="货物名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cargoInfo">
                 <a-input v-model="model.cargoInfo" placeholder="请输入货物名称"  ></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
                <a-form-model-item label="核查结果文件：" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="fileList">
                 <j-upload v-model="model.fileList" text="上传"></j-upload>
                </a-form-model-item>
            </a-col>
            <a-col :span="24">
              <a-form-model-item label="具体描述" :labelCol="{ span:2, offset:2}" :wrapperCol="{ span: 20, offset:0}" prop="detailDesc">
                 <a-textarea v-model="model.detailDesc" placeholder="请输入具体描述" :auto-size="{ minRows: 3, maxRows: 8 }"/>
              </a-form-model-item>
            </a-col>
        </a-row>

      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import JThirdAppDropdown from '@/components/jeecgbiz/thirdApp/JThirdAppDropdown'
  import mixedKeyboard from 'vehicle-keyboard/src/components/mixed-keyboard'
  export default {
    name: 'CheckSiteDataOverloadSpecialForm',
    components: {
        JThirdAppDropdown,
        mixedKeyboard
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        isDisabled:true,
         jCheckboxOptions: [
          {label: '是否', value: '1'},
        ],
        isshow:false,
        model:{},
        specialModel:{},
        formData:{},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 9 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 },
        },
        confirmLoading: false,
        modelDefault:{},
        validatorRules: {
           
        },
        args: {
          presetNumber: '', //预设车牌号码
          keyboardType: 0, //键盘类型[0,2]
          provinceName: '晋', //省份
          autoComplete: false
        },
        callbacks: {
          //// 回调函数
              onchanged: (presetNumber, isCompleted) => {
                // this.args.presetNumber = presetNumber;
                if(isCompleted)
                {
                    this.model.vehicleNo = presetNumber;
                    this.getBlur();
                }
                console.log(
                  '当前车牌[变更]：' + presetNumber + ', 已完成:' + isCompleted
                );
              },
              onkeypressed: key => {
                console.log('当前按键：' + key.text);
              },
              oncompleted: (presetNumber, isAutoCompleted) => {
                this.numberArray = presetNumber.split('');
                this.model.vehicleNo = presetNumber;
                this.getBlur();
                console.log(
                  '当前车牌[完成]：' + presetNumber + ', 自动完成:' + isAutoCompleted
                );
              },
              onmessage: function(message) {
                //console.info(message);
              }
        },
        url: {
          add: "/abnormal/specialCar/add",
          edit: "/abnormal/specialCar/edit",
          queryById: "/abnormal/specialCar/queryById",
          addSpecial: "/abnormal/specialCar/addSpecial"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      // this.modelDefault = JSON.parse(JSON.stringify(this.model));
      this.clickOther();
    },
    methods: {
      add (record) {
        this.edit(record);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
        this.modelDefault = JSON.parse(JSON.stringify(this.model));
      },
      getFocus(){
        this.isshow = true;
      },
      // 隐藏键盘
      getBlur(){
        this.isshow = false;
      },
      clickOther(){
         let _this = this;
         document.addEventListener('click',function(e){
         var inputFrom =  document.getElementById("specialPlateInput");
         if(inputFrom){
          if(!inputFrom.contains(e.target)){
            _this.isshow = false;
          }
         }
         
      });
      },
      parseForm(){
          this.specialModel.checkDataId = this.model.id;
          this.specialModel.siteCode = this.model.siteCode;
          this.specialModel.vehicleNo = this.model.vehicleNo;
          this.specialModel.plateColor = this.model.plateColor;
          this.specialModel.hangVehicleNo= this.model.hangVehicleNo;
          this.specialModel.axles = this.model.axles;
          this.specialModel.total = this.model.total;
          this.specialModel.overRate = this.model.overRate;
          this.specialModel.driverName = this.model.driverName;
          this.specialModel.driverIdCard= this.model.driverIdCard;
          this.specialModel.driverQuaCard= this.model.driverQuaCard;
          this.specialModel.driverQuaRegOrg = this.model.driverQuaRegOrg;
          this.specialModel.vehicleQuaCode = this.model.vehicleQuaCode;
          this.specialModel.vehicleRegOrg = this.model.vehicleRegOrg;
          this.specialModel.hangVehicleQuaCode = this.model.hangVehicleQuaCode;
          this.specialModel.hangVehicleRegOrg = this.model.hangVehicleRegOrg;
          this.specialModel.companyName = this.model.companyName;
          this.specialModel.unifiedCreditCode = this.model.unifiedCreditCode;
          this.specialModel.companyQuaCode = this.model.companyQuaCode;
          this.specialModel.companyRegOrg = this.model.companyRegOrg;
          this.specialModel.distOfStart = this.model.distOfStart;
          this.specialModel.distOfDestination = this.model.distOfDestination;
          this.specialModel.vehicleType = this.model.vehicleType;
          this.specialModel.cargoInfo = this.model.cargoInfo;
          this.specialModel.fileList = this.model.fileList;
          this.specialModel.detailDesc = this.model.detailDesc;
          this.specialModel.recorder = this.$store.getters.userInfo.username;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            this.parseForm();
            httpAction(this.url.addSpecial,this.specialModel,'post').then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    
    }
  }
</script>