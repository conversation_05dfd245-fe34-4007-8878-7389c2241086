<template>
    <div class="container">
        <dv-border-box-8 class="container_box">
            <div ref="myEchart" class="chart"></div>
        </dv-border-box-8>
    </div>
</template>
  
<script>
import moment from 'moment'
import Area from '@/components/_util/Area'
import { getAction } from '@/api/manage'
export default {
    name: 'CountyChart',
    components: { moment },
    data() {
        return {
            pcaa: this.$Jpcaa,
            chart: null,
            options: {},
            url: {
                chartUrl: "/bigScreen/siteCountyStatistics"
            },
            distNames:[],
            checkList:[],
            overList:[],
            overRateList:[],
        }
    },
    mounted() {
        this.initOptions()
        this.initCharts()
        this.getData();
    },
    methods: {
        moment,
        initOptions() {
            this.options = {
                title: {
                    text: '县区超限检测站超载数据统计',
                    top: '10px',
                    left: '10px',
                    textStyle: {
                        fontSize: '15',
                        fontWeight: '600',
                        color: '#fff',
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    left: 'center',
                    bottom: '0px',
                    data: ['检测数据', '超载数据', '超载率'],
                    textStyle: {
                        color: "#fff"
                    },
                    selectedMode: 'single', // 关键修改：设置为单选模式 
                    selected: {  // 新增的selected配置
                '检测数据': false,
                '超载数据': true,
                '超载率': false
            }
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '12%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        data: ['城区', '泽州县', '高平市', '阳城县', '沁水县', '陵川县'],
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#fff'
                            },
                            interval: 0
                        },
                        axisLine: {
                            show: true
                        },
                        boundaryGap: true // x轴两边是否留白
                    },

                ],
                yAxis: [
                    {
                        type: 'value',
                        splitLine: { show: false },//去除网格线
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#fff'
                            }
                        },
                        axisLine: {
                            show: true
                        }
                    },
                    {
                        type: 'value',
                        position: 'right',
                        splitLine: { show: false },
                        axisLabel: {
                            show: true,
                            textStyle: { color: '#fff' },
                            formatter: (value) => `${value}%`
                        },
                        axisLine: { show: true }
                    }
                ],
                series: [
                    {
                        name: '检测数据',
                        type: 'bar',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        yAxisIndex: 0, // 绑定左侧y轴
                        showSymbol: false,
                        data: [0, 0, 0, 0, 0, 0]
                    },
                    {
                        name: '超载数据',
                        type: 'bar',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        showSymbol: true,
                        yAxisIndex: 0, // 绑定左侧y轴
                        data: [0, 0, 0, 0, 0, 0]
                    },
                    {
                        name: '超载率',
                        type: 'line',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        yAxisIndex: 1, // 绑定左侧y轴
                        emphasis: {
                            focus: 'series'
                        },
                        showSymbol: false,
                        data: [0, 0, 0, 0, 0, 0]
                    },
                ]
            };
        },
        initCharts() {
            this.chart = this.$echarts.init(this.$refs.myEchart)
            this.chart.setOption(this.options)
        },
        getData() {
            getAction(this.url.chartUrl, { year: moment().format("YYYY-MM-DD") }).then((res) => {
                res.result.forEach(data => {
                    var dist = new Area(this.$Jpcaa).getText(data.distCode);
                    var arr =  dist.split("/");
                    this.distNames.push(arr[2]);
                    this.checkList.push(data.checkNum);
                    this.overList.push(data.overNum);
                    this.overRateList.push(data.overRate);
                   
                });
                this.options.xAxis[0].data = this.distNames;
                this.options.series[0].data = this.checkList;
                this.options.series[1].data = this.overList;
                this.options.series[2].data = this.overRateList;
                this.chart.setOption(this.options)
            });
        }
    }

}
</script>
  
<style lang="less">
.container {
    position: relative;

    // background: red;
    .container_box {
        width: 95%;
        height: 95%;
        margin: 0 auto;

        .chart {
            width: 100%;
            height: 100%;
        }
    }

}
</style>
  