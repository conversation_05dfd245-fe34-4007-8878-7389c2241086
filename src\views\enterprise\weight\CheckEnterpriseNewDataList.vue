<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业名称">
              <a-input placeholder="请输入企业名称" v-model="queryParam.enterpriseName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="县区编码">
              <j-area-linkage type="cascader" v-model="queryParam.distCode" placeholder="请选择省市区" />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="轴数">
                <j-dict-select-tag
                  placeholder="请选择轴数"
                  v-model="queryParam.axles"
                  dictCode="base_check_rule,axles,axles,is_default=1"
                />
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="检测时间">
                <j-date
                  :show-time="true"
                  date-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择开始时间"
                  class="query-group-cust"
                  v-model="queryParam.checkTime_begin"
                ></j-date>
                <span class="query-group-split-cust"></span>
                <j-date
                  :show-time="true"
                  date-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择结束时间"
                  class="query-group-cust"
                  v-model="queryParam.checkTime_end"
                ></j-date>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="是否超载">
                <j-dict-select-tag
                  placeholder="请选择是否超载"
                  v-model="queryParam.isOverload"
                  dictCode="iz_overload"
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('企业称重表')">导出</a-button>
      <j-super-query
        :fieldList="superFieldList"
        ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="btn" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <span  v-else @click="onClickPic(text)" style="font-size: 12px; cursor: pointer; color: cornflowerblue"
            >查看图片</span
          >
        </template>
        <template slot="pcaSlot" slot-scope="text">
          <div>{{ getPcaText(text) }}</div>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <template slot="plateSlot" slot-scope="text, record">
          <plate :plate="text" :color="Number(record.plateColor)"></plate>
        </template>
        <template slot="plateTailSlot" slot-scope="text, record">
          <plate :plate="text" :color="Number(record.plateColorTail)"></plate>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
        </span>
      </a-table>
    </div>
    <a-modal title="称重照片" :visible="visible" @ok="handleOk" @cancel="handleCancel">
      <div style="display: flex; justify-content: space-around">
        <div>
          <div style="font-size: 16px;    padding-bottom: 8px;">车头照片：</div>
          <vue-preview
            :slides="slides1"
            class="preview2"
          ></vue-preview>
        </div>
        <div>
          <div style="font-size: 16px;    padding-bottom: 8px;">车尾照片：</div>
          <vue-preview
            :slides="slides2"
            class="preview2"
          ></vue-preview>
        </div>
      </div>
    </a-modal>
    <check-enterprise-new-data-modal ref="modalForm" @ok="modalFormOk"></check-enterprise-new-data-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CheckEnterpriseNewDataModal from './modules/CheckEnterpriseNewDataModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import Area from '@/components/_util/Area'
import Plate from '@/components/plate/Plate'

export default {
  name: 'CheckEnterpriseNewDataList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CheckEnterpriseNewDataModal,
    Plate,
  },
  data() {
    return {
      description: '企业称重表管理页面',
      ModalText: 'Content of the modal',
      visible: false,
      confirmLoading: false,
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '企业名称',
          align: 'center',
          dataIndex: 'enterpriseName',
        },
        {
          title: '县区编码',
          align: 'center',
          dataIndex: 'distCode',
          scopedSlots: { customRender: 'pcaSlot' },
        },
        {
          title: '车牌号',
          align: 'center',
          dataIndex: 'vehicleNo',
          scopedSlots: { customRender: 'plateSlot' },
        },
        {
          title: '车尾号',
          align: 'center',
          dataIndex: 'vehicleNoTail',
          scopedSlots: { customRender: 'plateTailSlot' },
        },
        {
          title: '重量',
          align: 'center',
          dataIndex: 'weight',
        },
        {
          title: '检测时间',
          align: 'center',
          dataIndex: 'checkTime',
        },
        {
          title: '超载数',
          align: 'center',
          dataIndex: 'overWeight',
        },
        {
          title: '超载率',
          align: 'center',
          dataIndex: 'overRate',
        },
        {
          title: '是否超载',
          align: 'center',
          dataIndex: 'isOverload_dictText',
        },
        {
          title: '称重图片',
          align: 'center',
          dataIndex: 'imageListValue',
          scopedSlots: { customRender: 'btn' },
          // customRender: (text) => {
          //   let dataSource = [
          //     {
          //       src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
          //       type: '01',
          //     },
          //     {
          //       src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
          //       type: '31',
          //     },
          //     {
          //       src: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
          //       type: '81',
          //     },
          //   ]
          //   dataSource = dataSource.filter((i) => i.type == '01' || i.type == '31')
          //   // 使用 Vue 的 createElement (h) 构建虚拟节点
          //   console.log(dataSource.length > 0, ' dataSource.length > 0', dataSource, '==>data')
          //   let newslides = dataSource.map((i,index) => {
          //     return {
          //       w: 600, //设置以大图浏览时的宽度
          //       h: 400, //设置以大图浏览时的高度
          //       src: this.getImgView(i.src),
          //       msrc: this.getImgView(i.src),
          //     }
          //   })
          //   return this.$createElement('div', [
          //     dataSource.length == 0
          //       ? this.$createElement(
          //           'span',
          //           {
          //             style: { fontSize: '12px', fontStyle: 'italic' },
          //           },
          //           '无图片'
          //         )
          //       : this.$createElement('vue-preview', {
          //           attrs: {
          //             slides: newslides,
          //           },
          //           class: 'preview',
          //         }),
          //   ])
          // },
        },
        {
          title: '方向',
          align: 'center',
          dataIndex: 'dir_dictText',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/enterprise/weight/checkEnterpriseData/list',
        delete: '/enterprise/weight/checkEnterpriseData/delete',
        deleteBatch: '/enterprise/weight/checkEnterpriseData/deleteBatch',
        exportXlsUrl: '/enterprise/weight/checkEnterpriseData/exportXls',
        importExcelUrl: '/enterprise/weight/checkEnterpriseData/importExcel',
      },
      dictOptions: {},
      pcaData: '',
      superFieldList: [],
      slides1: [],
      slides2: [],
    }
  },
  created() {
    this.pcaData = new Area()
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    getPcaText(code) {
      return this.pcaData.getText(code)
    },
    initDictConfig() {},
    onClickPic: function (text) {
      console.log('查看图片', text)
      let list = text || [{src:'',}]
      let dataSource = list.filter((i) => i.type == '01' || i.type == '31')
      this.slides1 = [
        {
          w: 1200, //设置以大图浏览时的宽度
          h: 800, //设置以大图浏览时的高度
          src: dataSource[0].src,
          msrc: dataSource[0].src,
          title: '车头照片',
          alt: '车头照片',
        },
        {
          w: 1200, //设置以大图浏览时的宽度
          h: 800, //设置以大图浏览时的高度
          src: dataSource[1].src,
          title: '车尾照片',
          alt: '车尾照片',
          visible: false,
        },
      ]
      this.slides2 = [
        {
          w: 1200, //设置以大图浏览时的宽度
          h: 800, //设置以大图浏览时的高度
          src: dataSource[1].src,
          msrc: dataSource[1].src,
          title: '车尾照片',
          alt: '车尾照片',
        },
        {
          w: 1200, //设置以大图浏览时的宽度
          h: 800, //设置以大图浏览时的高度
          src: dataSource[0].src,
          title: '车头照片',
          alt: '车头照片',
        },
      ]
      console.log(dataSource, 'dataSource')
      this.visible = true
      //01 车头 31 车尾
    },
    handleOk(e) {
      this.visible = false
    },
    handleCancel(e) {
      this.visible = false
    },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'code', text: '检测编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'enterpriseName', text: '企业名称', dictCode: '' })
      fieldList.push({ type: 'pca', value: 'distCode', text: '县区编码' })
      fieldList.push({ type: 'string', value: 'enterpriseCode', text: '企业编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicleNo', text: '车牌号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'plateColor', text: '车牌颜色', dictCode: 'plate_color' })
      fieldList.push({ type: 'string', value: 'equCode', text: '设备编码(仪表编码)', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicleNoTail', text: '车尾号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'plateColorTail', text: '车尾颜色', dictCode: 'plate_color' })
      fieldList.push({ type: 'string', value: 'axles', text: '轴数', dictCode: 'base_check_rule,axles,axles' })
      fieldList.push({ type: 'string', value: 'axleType', text: '轴型', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight', text: '重量', dictCode: '' })
      fieldList.push({ type: 'int', value: 'length', text: '长', dictCode: '' })
      fieldList.push({ type: 'int', value: 'width', text: '宽', dictCode: '' })
      fieldList.push({ type: 'int', value: 'height', text: '高', dictCode: '' })
      fieldList.push({ type: 'int', value: 'overWidth', text: '超宽', dictCode: '' })
      fieldList.push({ type: 'int', value: 'overHeight', text: '超高', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'checkTime', text: '检测时间' })
      fieldList.push({ type: 'int', value: 'overLength', text: '超长', dictCode: '' })
      fieldList.push({ type: 'int', value: 'overWeight', text: '超载数', dictCode: '' })
      fieldList.push({ type: 'BigDecimal', value: 'overRate', text: '超载率', dictCode: '' })
      fieldList.push({ type: 'string', value: 'isOverload', text: '是否超载', dictCode: 'iz_overload' })
      fieldList.push({ type: 'string', value: 'izFinish', text: '是否完成', dictCode: 'iz_finish' })
      fieldList.push({ type: 'string', value: 'licNo', text: '大件运输证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'isBulkVehicle', text: '是否大件', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dir', text: '检查方向', dictCode: 'check_dir' })
      fieldList.push({ type: 'string', value: 'dirLinkCode', text: '关联入厂code(出厂必填)', dictCode: '' })
      fieldList.push({ type: 'int', value: 'limitWeight', text: '限重', dictCode: '' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style>
@import '~@assets/less/common.less';
.preview2 figure img {
  width: 120px;
  height: 120px;
}
.preview2 figure:nth-child(2) {
  display: none;
}
</style>
