<template>
  <a-spin :spinning="confirmLoading" :model="model">
    <a-col :span="24">
      <template>
        <div v-for="(fileDetail, index) in this.model.images" :key="index">
          <div style="float: left;width:175px;height:175px;margin-right: 10px;margin: 0 8px 8px 0; text-align:center">
            <div
              style="width: 100%;height: 100%;position: relative;padding: 8px;border: 1px solid #d9d9d9;border-radius: 4px;">
              <img style="width: 100%;" :src="fileDetail.dataUrl" preview="0" :onerror="defaultImg">
              <span style="text-aligin:center">{{ fileDetail.fileName }}</span>
            </div>
          </div>
        </div>
      </template>
    </a-col>
    <a-col :span="24">
      <a-card>
        <detail-list title="车辆信息">
          <detail-list-item term="检测站点">
            <j-ellipsis :value="this.model.siteName" :length="10" />
          </detail-list-item>
          <detail-list-item term="行政区划">{{ this.getPcaText(this.model.distCode) }}</detail-list-item>
          <detail-list-item term="检测类型">{{ this.model.checkType_dictText }}</detail-list-item>
          <detail-list-item term="车道号">{{ this.model.laneNumber }}</detail-list-item>
          <detail-list-item term="车道方向" v-if="this.model.baseSitePoint != null">{{ this.model.baseSitePoint.direction }}
          </detail-list-item>
          <detail-list-item term="车牌号">{{ this.model.vehicleNo }}</detail-list-item>
          <detail-list-item term="车牌颜色">{{ this.model.plateColor_dictText }}</detail-list-item>
          <detail-list-item term="检测时间">{{ this.model.checkTime }}</detail-list-item>
          <detail-list-item term="轴数">{{ this.model.axles }}</detail-list-item>
          <detail-list-item term="轴型">{{ this.model.vehicleAxlesType }}</detail-list-item>
          <detail-list-item term="实重">{{ this.model.total }}</detail-list-item>
          <detail-list-item term="限重">{{ this.model.limitWeight }}</detail-list-item>
          <div v-if="this.model.isOverload == 1">
            <detail-list-item term="超重">{{ this.model.overWeight }}</detail-list-item>
            <detail-list-item term="超载率">{{ this.model.overRate }}</detail-list-item>
          </div>
          <detail-list-item term="车辆类型">{{ this.model.vehicleListType_dictText }}</detail-list-item>
          <detail-list-item term="是否大件">{{ this.model.isBulkVehicle_dictText }}</detail-list-item>
          <detail-list-item term="轴一重量">{{ this.model.weight1 }}</detail-list-item>
          <detail-list-item term="轴二重量">{{ this.model.weight2 }}</detail-list-item>
          <detail-list-item term="轴三重量">{{ this.model.weight3 }}</detail-list-item>
          <detail-list-item term="轴四重量">{{ this.model.weight4 }}</detail-list-item>
          <detail-list-item term="轴五重量">{{ this.model.weight5 }}</detail-list-item>
          <detail-list-item term="轴六重量">{{ this.model.weight6 }}</detail-list-item>
          <detail-list-item term="其他轴重">{{ this.model.weight7 }}</detail-list-item>
        </detail-list>
        <a-divider style="margin: 16px 0"></a-divider>
        <detail-list title="补充信息">
          <detail-list-item term="是否要情报告单">{{ this.model.severe_dictText }}</detail-list-item>
          <detail-list-item term="移交单位">{{ this.model.handoverDepartment }}</detail-list-item>
          <detail-list-item term="货物">{{ this.model.goodsCode_dictText }}</detail-list-item>
          <detail-list-item term="货物详情">{{ this.model.goodsDetail }}</detail-list-item>
          <detail-list-item term="备注">{{ this.model.remark }}</detail-list-item>
        </detail-list>
        <a-divider style="margin-bottom: 32px" />
        <div v-if="isShowLog">
          <div class="title">{{ title }}</div>
          <a-table style="margin-bottom: 24px" :columns="carColumns" :data-source="carData">
            <a slot="name" slot-scope="text">{{ text }}</a>
          </a-table>
        </div>
      </a-card>
    </a-col>
  </a-spin>
</template>
<script>
import ARow from 'ant-design-vue/es/grid/Row'
import DetailList from '@/components/tools/DetailList'
import Area from '@/components/_util/Area'
import STable from '@/components/table/'
import { postAction } from '@/api/manage'
const DetailListItem = DetailList.Item
export default {
  name: 'CheckSiteDataOverloadDetail',
  components: {
    ARow, DetailList, DetailListItem, STable
  },
  data() {
    return {
      spinning: false,
      confirmLoading: false,
      title: "卸货记录",
      model: {
        "baseSitePoint": {
          direction: "",
        }
      },
      isShowLog: false,
      url: {
        repeatUrl: "/truck/checkSiteDataOverload/repeat",
        unloadUrl: "/truck/checkSiteDataOverload/unloading"
      },
      dataSource: [{
        key: 0,
        fileDetails: [
          {
            imgUrl: "https://static.jeecg.com/upload/test/3a4490d5d1cd495b826e528537a47cc1.jpg"
          },

        ]
      }],
      carData: [],
      carColumns: [
        {
          title: '站点名称',
          dataIndex: 'siteName',
          key: 'siteName'
        },
        {
          title: '检测时间',
          dataIndex: 'checkTime',
          key: 'checkTime'
        },
        {
          title: '车牌号',
          dataIndex: 'vehicleNo',
          key: 'vehicleNo'
        },
        {
          title: '轴数',
          dataIndex: 'axles',
          key: 'axles'
        },
        {
          title: '总重',
          dataIndex: 'total',
          key: 'total',
        },
        {
          title: '限重',
          dataIndex: 'limitWeight',
          key: 'limitWeight',
        },
        {
          title: '是否超载',
          dataIndex: 'isOverload',
          key: 'isOverload',
        }
      ],

    }
  },
  props: {},
  computed: {
    defaultImg() {
      return 'this.src="' + require("@assets/logo.png") + '"';
    },
  },
  created() {
    this.$previewRefresh()
    this.pcaData = new Area(this.$Jpcaa)
  },
  methods: {
    //解析行政区划
    getPcaText(code) {
      return this.pcaData.getText(code);
    },
    show(record) {
      this.model = Object.assign({}, record);
      if (this.model.overType == 4) {
        this.title = "卸货记录";
        this.isShowLog = true;
        this.getUnloadData();
      }
      if (this.model.overType == 2) {
        this.title = "复磅记录";
        this.isShowLog = true;
        this.getRepeatData();
      }

    },
    getUnloadData() {
      postAction(this.url.unloadUrl, this.model).then(res => {
        if (res.success) {
          this.carData = res.result;
        }
      });
    },
    getRepeatData() {
      postAction(this.url.repeatUrl, this.model).then(res => {
        if (res.success) {
          this.carData = res.result;
        }
      });
    }
  },
}
</script>
<style lang="less" scoped>
.title {
  color: rgba(0, 0, 0, .85);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}
</style>