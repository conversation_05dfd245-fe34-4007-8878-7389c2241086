<template>
    <div ref="myEchart" class="chart"></div>
</template>
  
<script>
import { getAction } from '@/api/manage'
export default {
    name: 'SiteLineChart',
    data() {
        return {
            chart: null,
            options: {},
            url: {
                siteUrl: "/bigScreen/siteCheckDataByDays",
                enterpriseUrl: "/bigScreen/enterpriseCheckDataByDays",
                unmannedUrl: "/bigScreen/unmannedCheckDataByDays",

            },
            dayList: [],
            checkNumList: [],
            overNumList: [],
            overRateList: [],
        }
    },
    mounted() {
        this.initOptions()
        this.initCharts()
    },
    methods: {
        getData2(code, offset, type) {
            var url = "";
            if (type == "site") {
                url = this.url.siteUrl;
            } else if (type == "enterprise") {
                url = this.url.enterpriseUrl;
            } else if (type == "unmanned") {
                url = this.url.unmannedUrl;
            }
            getAction(url, { code: code, offset: offset }).then((res) => {
                if(res.success){
                    if (res.result.length > 0) {
                    res.result.forEach((data) => {
                        this.dayList.push(data["time"]);
                        this.checkNumList.push(data["checkNum"]);
                        this.overNumList.push(data["overNum"]);
                        this.options.xAxis[0].data = this.dayList;
                        this.options.series[0].data = this.checkNumList;
                        this.options.series[1].data = this.overNumList;
                        this.chart.setOption(this.options)
                    });
                }
                }
               

            });

        },

        initOptions() {
            this.options = {
                title: {
                    text: '15日过车统计',
                    top: '10px',
                    left: '10px',
                    textStyle: {
                        fontSize: '15',
                        fontWeight: '600',
                        color: '#fff',
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    left: 'center',
                    bottom: '0px',
                    data: ['检测数据', '超载数据'],
                    textStyle: {
                        color: "#fff"
                    },
                    selectedMode: 'single', // 关键修改：设置为单选模式
                    selected: {  // 新增的selected配置
                '检测数据': false,
                '超载数据': true,
            }
                },
                // grid: {
                //     left: '3%',
                //     right: '3%',
                //     bottom: '13%',
                //     containLabel: true
                // },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        data: [],
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#fff'
                            },
                            interval: 0
                        },
                        axisLine: {
                            show: true
                        }
                    },

                ],
                yAxis: [
                    {
                        type: 'value',
                        splitLine: { show: false },//去除网格线
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: '#fff'
                            }
                        },
                        axisLine: {
                            show: true
                        }
                    },

                ],
                series: [
                    {
                        name: '检测数据',
                        type: 'line',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        showSymbol: false,
                        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },
                    {
                        name: '超载数据',
                        type: 'line',
                        smooth: true,//变为曲线 默认false折线
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        showSymbol: true,
                        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },

                ],
                grid: { // 让图表占满容器
                    top: "50px",
                    left: "50px",
                    right: "50px",
                    bottom: "50px"
                }
            };
        },
        initCharts() {
            this.chart = this.$echarts.init(this.$refs.myEchart)
            this.chart.setOption(this.options)
        }
    }

}
</script>
  
<style lang="less">
.chart {
    height: 100%; /* 高度由父容器决定 */
    min-height: 285px; /* 可设置最小高度防止过小 */
    width: 100%;
}
// .chart canvas{
//     height: 245px;
// }
</style>
  