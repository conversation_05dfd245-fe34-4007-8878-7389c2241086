<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">

          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="系统状态">
              <j-dict-select-tag placeholder="请选择系统状态" v-model="queryParam.status" dictCode="online_status" />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="上传状态">
              <j-dict-select-tag placeholder="请选择上传状态" v-model="queryParam.status" dictCode="upload_status" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
            selectedRowKeys.length
        }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
         <template slot="pcaSlot" slot-scope="text">
          <div>{{ getPcaText(text) }}</div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <city-platform-status-info-modal ref="modalForm" @ok="modalFormOk"></city-platform-status-info-modal>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CityPlatformStatusInfoModal from './modules/CityPlatformStatusInfoModal'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import Area from '@/components/_util/Area'

export default {
  name: 'CityPlatformStatusInfoList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CityPlatformStatusInfoModal
  },
  data() {
    return {
      description: '市级系统状态消息管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function (t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '行政区划代码',
          align: "center",
          dataIndex: 'distCode',
        },
        {
          title: '系统状态',
          align: "center",
          dataIndex: 'status_dictText'
        },
        {
          title: '消息发送时间',
          align: "center",
          dataIndex: 'sendTime'
        },
        {
          title: '上传状态',
          align: "center",
          dataIndex: 'isUpload_dictText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          fixed: "right",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: "/status/cityPlatformStatusInfo/list",
        delete: "/status/cityPlatformStatusInfo/delete",
        deleteBatch: "/status/cityPlatformStatusInfo/deleteBatch",
        exportXlsUrl: "/status/cityPlatformStatusInfo/exportXls",
        importExcelUrl: "status/cityPlatformStatusInfo/importExcel",

      },
      dictOptions: {},
      pcaData: '',
      superFieldList: [],
    }
  },
  created() {
    this.pcaData = new Area();

    this.getSuperFieldList();
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    initDictConfig() {
    },
    getPcaText(code) {
      return this.pcaData.getText(code)
    },
    getSuperFieldList() {
      let fieldList = [];
      fieldList.push({ type: 'string', value: 'uniqueId', text: '数据唯一标识', dictCode: '' })
      fieldList.push({ type: 'string', value: 'distCode', text: '行政区划代码', dictCode: '' })
      fieldList.push({ type: 'int', value: 'status', text: '系统状态', dictCode: 'online_status' })
      fieldList.push({ type: 'datetime', value: 'sendTime', text: '消息发送时间' })
      this.superFieldList = fieldList
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>