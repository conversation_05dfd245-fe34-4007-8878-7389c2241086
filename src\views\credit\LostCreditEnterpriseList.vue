<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="上报年份">
              <a-input placeholder="请输入上报年份" v-model="queryParam.year"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="上报周期">
              <j-dict-select-tag placeholder="请选择上报周期" v-model="queryParam.season" dictCode="season"/>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="企业名称">
                <a-input placeholder="请输入企业名称" v-model="queryParam.companyName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="发布期开始日期">
                <j-date placeholder="请选择发布期开始日期" v-model="queryParam.releaseStartDate"></j-date>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('运输企业失信名单信息表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <lost-credit-enterprise-modal ref="modalForm" @ok="modalFormOk"></lost-credit-enterprise-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import LostCreditEnterpriseModal from './modules/LostCreditEnterpriseModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'LostCreditEnterpriseList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      LostCreditEnterpriseModal
    },
    data () {
      return {
        description: '运输企业失信名单信息表管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'上报年份',
            align:"center",
            dataIndex: 'year'
          },
          {
            title:'上报周期',
            align:"center",
            dataIndex: 'season_dictText'
          },
          {
            title:'企业名称',
            align:"center",
            dataIndex: 'companyName'
          },
          {
            title:'统一社会信用代码',
            align:"center",
            dataIndex: 'unifiedCreditCode'
          },
          {
            title:'道路运输经营许可证号',
            align:"center",
            dataIndex: 'companyQuaCode'
          },
          {
            title:'法定代表人',
            align:"center",
            dataIndex: 'legalRepresentative'
          },
          {
            title:'身份证号',
            align:"center",
            dataIndex: 'idCardNumber'
          },
          {
            title:'货运车辆总数',
            align:"center",
            dataIndex: 'totalNumberOfVehicles'
          },
          {
            title:'违法超限运输车辆数量',
            align:"center",
            dataIndex: 'numberOfIllegalVehicles'
          },
          {
            title:'发布期开始日期',
            align:"center",
            dataIndex: 'releaseStartDate',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'发布期结束日期',
            align:"center",
            dataIndex: 'releaseEndDate',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'公示地址',
            align:"center",
            dataIndex: 'publishAddress'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/credit/lostCreditEnterprise/list",
          delete: "/credit/lostCreditEnterprise/delete",
          deleteBatch: "/credit/lostCreditEnterprise/deleteBatch",
          exportXlsUrl: "/credit/lostCreditEnterprise/exportXls",
          importExcelUrl: "credit/lostCreditEnterprise/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'year',text:'上报年份',dictCode:''})
        fieldList.push({type:'string',value:'season',text:'上报周期',dictCode:'season'})
        fieldList.push({type:'string',value:'companyName',text:'企业名称',dictCode:''})
        fieldList.push({type:'string',value:'unifiedCreditCode',text:'统一社会信用代码',dictCode:''})
        fieldList.push({type:'string',value:'companyQuaCode',text:'道路运输经营许可证号',dictCode:''})
        fieldList.push({type:'string',value:'legalRepresentative',text:'法定代表人',dictCode:''})
        fieldList.push({type:'string',value:'idCardNumber',text:'身份证号',dictCode:''})
        fieldList.push({type:'int',value:'totalNumberOfVehicles',text:'货运车辆总数',dictCode:''})
        fieldList.push({type:'int',value:'numberOfIllegalVehicles',text:'违法超限运输车辆数量',dictCode:''})
        fieldList.push({type:'date',value:'releaseStartDate',text:'发布期开始日期'})
        fieldList.push({type:'date',value:'releaseEndDate',text:'发布期结束日期'})
        fieldList.push({type:'string',value:'publishAddress',text:'公示地址',dictCode:''})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>