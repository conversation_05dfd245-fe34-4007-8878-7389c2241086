<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-form-item label="企业名称">
              <j-search-select-tag placeholder="请选择企业" v-model="queryParam.siteCode"
                dict="base_enterprise,enterprise_name,code" :pageSize="20" :async="true" />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="车辆号牌">
              <a-input placeholder="请输入车辆号牌" v-model="queryParam.vehicleNo"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="7" :lg="11" :md="18" :sm="24">
            <a-form-item label="检测时间">
              <j-date-range v-model="date" style="width:100%" @change="dateRangeChange"></j-date-range>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="车辆轴数">
                <j-dict-select-tag placeholder="请选择车辆轴数" v-model="queryParam.axles" dictCode="axles" />
              </a-form-item>
            </a-col>
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="县区">
                <j-area-linkage type="cascader" v-model="queryParam.distCode" placeholder="请选择省市区" />
              </a-form-item>
            </a-col>
            <a-col :xl="4" :lg="7" :md="5" :sm="24">
              <a-form-item label="超载类型">
                <j-dict-select-tag placeholder="请选择是否超载" v-model="queryParam.isOverload" dictCode="over_type" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="2" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button type="primary" icon="download" @click="handleExportXls('企业超载数据表')">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery">
      </j-super-query>
      <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown> -->
      <a-button type="primary" icon="warning" @click="showAbnormalModal">异常数据列表</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        <span style="float: right">
          <a @click="loadData()">
            <a-icon type="sync" />刷新
          </a>
          <a-divider type="vertical" />
          <a-popover title="自定义列" trigger="click" placement="leftBottom">
            <template slot="content">
              <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                <a-row style="width: 400px">
                  <template v-for="(item, index) in defColumns">
                    <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                      <a-col :span="12">
                        <a-checkbox :value="item.dataIndex">
                          <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                        </a-checkbox>
                      </a-col>
                    </template>
                  </template>
                </a-row>
              </a-checkbox-group>
            </template>
            <a>
              <a-icon type="setting" />设置
            </a>
          </a-popover>
        </span>
      </div>

      <a-table ref="table" size="middle" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" class="j-table-force-nowrap"
        @change="handleTableChange">
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic" />
        </template>
        <template slot="pcaSlot" slot-scope="text">
          <div>{{ getPcaText(text) }}</div>
        </template>
        <template slot="plateSlot" slot-scope="text, record">
          <plate :plate="text" :color="record.plateColor"></plate>
        </template>
        <template slot="status" slot-scope="text, record">
          <edit-table-cell-select :text="text" :records="record" @change="onCellChange(record.id, 'overType', $event)">
          </edit-table-cell-select>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleAbnormal(record)">异常修改</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <check-enterprise-data-overload-modal ref="modalForm" @ok="modalFormOk"></check-enterprise-data-overload-modal>
    <abnormal-enterprise-modal ref="abnormalModal" @ok="modalFormOk"></abnormal-enterprise-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import Vue from 'vue'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CheckEnterpriseDataOverloadModal from './modules/enterprise/CheckEnterpriseDataOverloadModal.Style#Drawer'
import { filterMultiDictText } from '@/components/dict/JDictSelectUtil'
import Area from '@/components/_util/Area'
import Plate from '@/components/plate/Plate'
import AbnormalEnterpriseModal from './modules/modal/AbnormalEnterpriseModal'
import EditTableCellSelect from '@/components/EditableCell/select1'
import { httpAction, getAction } from '@/api/manage'
import moment from 'moment'
import JDateRange from '@/components/jeecg/JDateRange'

export default {
  name: 'CheckEnterpriseDataOverloadList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CheckEnterpriseDataOverloadModal,
    Plate,
    AbnormalEnterpriseModal,
    EditTableCellSelect,
    JDateRange
  },
  data() {
    return {
      description: '企业超载数据表管理页面',
      //表头
      columns: [],
      //列设置
      settingColumns: [],
      date: [],
      // 表头
      defColumns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '企业名称',
          align: 'center',
          dataIndex: 'siteName',
        },
        {
          title: '行政区划',
          align: 'center',
          dataIndex: 'distCode',
          scopedSlots: { customRender: 'pcaSlot' },
        },
        {
          title: '车道号',
          align: 'center',
          dataIndex: 'laneNumber',
        },
        {
          title: '检测时间',
          align: 'center',
          dataIndex: 'checkTime',
          sorter: true,
          sortDirections: ['descend', 'ascend', 'descend'],
          // 配置默认是倒序
          defaultSortOrder: 'descend',
        },
        {
          title: '车辆号牌',
          align: 'center',
          dataIndex: 'vehicleNo',
          scopedSlots: { customRender: 'plateSlot' },
        },
        {
          title: '轴型',
          align: 'center',
          dataIndex: 'vehicleAxlesType',
        },
        {
          title: '总重',
          align: 'center',
          dataIndex: 'total',
        },
        {
          title: '轴数',
          align: 'center',
          dataIndex: 'axles',
          customRender: function (t, r, index) {
            return t + '轴'
          },
        },
        {
          title: '限重',
          align: 'center',
          dataIndex: 'limitWeight',
          customRender: function (t, r, index) {
            return t / 1000 + '吨'
          },
        },
        {
          title: '超限量',
          align: 'center',
          dataIndex: 'overWeight',
          customRender: function (t, r, index) {
            return t / 1000 + '吨'
          },
        },
        {
          title: '超载率',
          align: 'center',
          dataIndex: 'overRate',
          customRender: function (t, r, index) {
            return t + '%'
          },
        },
        // {
        //   title: '是否为大件',
        //   align: 'center',
        //   dataIndex: 'isBulkVehicle_dictText',
        // },
        // {
        //   title: '车辆名单类型',
        //   align: 'center',
        //   dataIndex: 'vehicleListType_dictText',
        // },
        {
          title: '超载类型',
          align: 'center',
          dataIndex: 'overType',
          scopedSlots: { customRender: 'status' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/truck/checkEnterpriseDataOverload/list',
        edit: '/truck/checkEnterpriseDataOverload/edit',
        delete: '/truck/checkEnterpriseDataOverload/delete',
        deleteBatch: '/truck/checkEnterpriseDataOverload/deleteBatch',
        exportXlsUrl: '/truck/checkEnterpriseDataOverload/exportXls',
        importExcelUrl: 'truck/checkEnterpriseDataOverload/importExcel',
      },
      dictOptions: {},
      pcaData: '',
      superFieldList: [],
    }
  },
  created() {
    this.pcaData = new Area()
    this.getSuperFieldList()
    this.initColumns()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    moment,
    getPcaText(code) {
      return this.pcaData.getText(code)
    },
    initDictConfig() { },
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'checkNo', text: '检测单号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'siteName', text: '治超站', dictCode: '' })
      fieldList.push({ type: 'string', value: 'siteCode', text: '站点编号', dictCode: '' })
      fieldList.push({ type: 'pca', value: 'distCode', text: '行政区划' })
      fieldList.push({ type: 'string', value: 'laneNumber', text: '检测车道编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'equipCode', text: '称重检测设备编号', dictCode: '' })
      fieldList.push({ type: 'int', value: 'checkType', text: '检测数据类型', dictCode: 'check_type' })
      fieldList.push({ type: 'datetime', value: 'checkTime', text: '检测时间' })
      fieldList.push({ type: 'string', value: 'linkCheckNo', text: '关联检测单号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicleNo', text: '车辆号牌', dictCode: '' })
      fieldList.push({ type: 'int', value: 'plateColor', text: '车牌颜色', dictCode: 'plate_color' })
      fieldList.push({ type: 'string', value: 'vehicleAxlesType', text: '车型代码', dictCode: '' })
      fieldList.push({ type: 'int', value: 'total', text: '车货总质量', dictCode: '' })
      fieldList.push({ type: 'int', value: 'axles', text: '车辆轴数', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight1', text: '轴重1', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight2', text: '轴重2', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight3', text: '轴重3', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight4', text: '轴重4', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight5', text: '轴重5', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight6', text: '轴重6', dictCode: '' })
      fieldList.push({ type: 'int', value: 'weight7', text: '轴重7', dictCode: '' })
      fieldList.push({ type: 'int', value: 'speed', text: '入口车速', dictCode: '' })
      fieldList.push({ type: 'int', value: 'limitWeight', text: '最大允许总质量', dictCode: '' })
      fieldList.push({ type: 'int', value: 'overWeight', text: '超限量', dictCode: '' })
      fieldList.push({ type: 'BigDecimal', value: 'overRate', text: '超限超载率', dictCode: '' })
      fieldList.push({ type: 'int', value: 'unloadWeight', text: '卸载重量', dictCode: '' })
      fieldList.push({ type: 'int', value: 'totalLength', text: '车货总长度', dictCode: '' })
      fieldList.push({ type: 'int', value: 'totalWidth', text: '车货总宽度', dictCode: '' })
      fieldList.push({ type: 'int', value: 'totalHeight', text: '车货总高度', dictCode: '' })
      fieldList.push({ type: 'int', value: 'overLength', text: '超长量', dictCode: '' })
      fieldList.push({ type: 'int', value: 'overWidth', text: '超宽量', dictCode: '' })
      fieldList.push({ type: 'int', value: 'overHeight', text: '超高量', dictCode: '' })
      fieldList.push({ type: 'int', value: 'isBulkVehicle', text: '是否为大件运输车辆', dictCode: 'is_bulk_vehicle' })
      fieldList.push({ type: 'string', value: 'licNo', text: '匹配的大件运输许可证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicleListType', text: '车辆名单类型', dictCode: 'vehicle_list_type' })
      fieldList.push({ type: 'int', value: 'overType', text: '超载类型', dictCode: '' })
      this.superFieldList = fieldList
    },
    onCellChange(key, filed, value) {
      var obj = {
        id: key,
        overType: value,
      }
      httpAction(this.url.edit, obj, 'put')
        .then((res) => {
          if (res.success) {
            this.$message.success('超载状态修改成功！')
          }
        })
        .catch((res) => {
          this.$message.error('超载状态修改失败！')
        })
    },
    //列设置更改事件
    onColSettingsChange(checkedValues) {
      var key = this.$route.name + ':colsettings'
      Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      this.settingColumns = checkedValues
      const cols = this.defColumns.filter((item) => {
        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          return true
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          return true
        }
        return false
      })
      this.columns = cols
    },
    showAbnormalModal() {
      this.$refs.abnormalModal.showModal()
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');
      var key = this.$route.name + ':colsettings'
      let colSettings = Vue.ls.get(key)
      if (colSettings == null || colSettings == undefined) {
        let allSettingColumns = []
        this.defColumns.forEach(function (item, i, array) {
          allSettingColumns.push(item.dataIndex)
        })
        this.settingColumns = allSettingColumns
        this.columns = this.defColumns
      } else {
        this.settingColumns = colSettings
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true
          }
          if (colSettings.includes(item.dataIndex)) {
            return true
          }
          return false
        })
        this.columns = cols
      }
    },
    handleDetail: function (record) {
      this.$refs.modalForm.show(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },
    dateRangeChange(dateStr) {
      this.date = dateStr;
      this.queryParam.checkTime_begin = dateStr[0]
      this.queryParam.checkTime_end = dateStr[1]
    },
    handleAbnormal(record) {
      this.$refs.modalForm.abnormalAdd(record)
      this.$refs.modalForm.title = '异常车辆信息登记'
      this.$refs.modalForm.disableSubmit = false
    }
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>