<template>
  <div class="editable-cell" >
        <div v-if="editable" class="editable-cell-input-wrapper">
          <a-select :default-value="value" @change="handleChange" @pressEnter="check" style="width: 100px">
            <a-select-option value="3" :disabled="value== 3">
              等待卸货
            </a-select-option>
            <a-select-option value="4" :disabled="value== 4">
              已卸货
            </a-select-option>
            <a-select-option value="5" :disabled="value== 5">
              超载
            </a-select-option>
            <a-select-option value="6" :disabled="value== 6">
              跳轴
            </a-select-option>
            <a-select-option value="7" :disabled="value== 7">
              小于一吨
            </a-select-option>
            <a-select-option value="8" :disabled="value== 8">
              大于一吨
            </a-select-option>
            <a-select-option value="9" :disabled="value== 9">
              其它
            </a-select-option>
          </a-select>
          <a-icon
            type="check"
            class="editable-cell-icon-check"
            @click="check"
          />
        </div>
        <div v-else class="editable-cell-text-wrapper">
          <a-tag :color="color" style="font-size:14px;padding:3px 10px">
          {{ value | tranStatus() }}
          </a-tag>
          <a-icon type="edit" class="editable-cell-icon" @click="edit" v-if="records.passStatusType != 0"/>
        </div>
  </div>

</template>
<script>
  export default {
    name: "EditTableCellSelect",
    props: {
      text: Number,
      records: Object,
      dvalue:String,
    },
    data() {
      return {
        value: this.text,
        editable: false,
        color:"#FF0000"
      };
    },
    created(){
      if(typeof(this.text) != 'string')
      {
        this.value = (this.text).toString();
      }
      this.tranTag(this.value)
    },
    watch:{
      value(val, oldVal){//普通的watch监听
          this.tranTag(val);
      },
    },
    methods: {
      handleChange(e) {
      this.value =  e;
      },
      check() {
        this.editable = false;
        this.$emit('change', this.value);
      },
      edit() {
        this.editable = true;
      },
      tranTag(val){
          switch(val){
             case '0':
                this.color = "#00ff00"
                break;
              case '1':
                this.color = "#FFD700"
                break;
              case '2':
                  this.color = "#00BFFF"
                break;
              case '3':
                  this.color = "#FFA500"
                break;
              case '4':
                  this.color = "#00FF00"
                break;
              case '5':
                this.color = "#FF0000"
                break;
              case '7':
                this.color = "#000051"
                break;
          }
      }

    },
    filters:{
      tranStatus(val) {
          switch(val){
             case '0':
                return "正常";
              case '1':
                return "等待复磅";
              case '2':
                return "已复磅";
              case '3':
                return "等待卸货";
              case '4':
                return "已卸货";
              case '5':
                return "超载";
              case '6':
                return "跳轴";
              case '7':
                return "小于一吨";
              case '8':
                return "大于一吨";
              case '9':
                return "其他";
          }
      },
    }
  }
</script>
<style scoped>

.editable-cell {
  position: relative;
}
.editable-cell-input-wrapper,
.editable-cell-text-wrapper {
  padding-right: 20px;
}
.editable-cell-text-wrapper {
  padding: 5px 20px 5px 5px;
}
.editable-cell-icon,
.editable-cell-icon-check {
  position: absolute;
  right: 0;
  width: 20px;
  cursor: pointer;
}
.editable-cell-icon {
  line-height: 25px;
  display: none;
}
.editable-cell-icon-check {
  line-height: 35px;
}
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}
.editable-cell-icon:hover,
.editable-cell-icon-check:hover {
  color: #108ee9;
}
.editable-add-btn {
  margin-bottom: 8px;
}
</style>

