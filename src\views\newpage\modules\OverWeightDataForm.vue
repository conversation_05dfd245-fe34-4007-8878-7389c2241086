<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="企业编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enterpriseCode">
              <a-input v-model="model.enterpriseCode" placeholder="请输入企业编码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="检测单号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="checkNo">
              <a-input v-model="model.checkNo" placeholder="请输入检测单号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="设别编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="equipCode">
              <a-input v-model="model.equipCode" placeholder="请输入设别编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车头车牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicle">
              <a-input v-model="model.vehicle" placeholder="请输入车头车牌号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车头车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="color">
              <a-input v-model="model.color" placeholder="请输入车头车牌颜色"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车尾车牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tailVehicle">
              <a-input v-model="model.tailVehicle" placeholder="请输入车尾车牌号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车尾车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tailColor">
              <a-input v-model="model.tailColor" placeholder="请输入车尾车牌颜色"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车道号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="landId">
              <a-input v-model="model.landId" placeholder="请输入车道号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="轴数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axles">
              <a-input v-model="model.axles" placeholder="请输入轴数"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="轴型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="axleType">
              <a-input v-model="model.axleType" placeholder="请输入轴型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicleType">
              <a-input v-model="model.vehicleType" placeholder="请输入车型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="限重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="limitWeight">
              <a-input v-model="model.limitWeight" placeholder="请输入限重"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="总重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="total">
              <a-input v-model="model.total" placeholder="请输入总重"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="超重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overWeight">
              <a-input v-model="model.overWeight" placeholder="请输入超重"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="超载率" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overRate">
              <a-input v-model="model.overRate" placeholder="请输入超载率"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="超载标识" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overFlag">
              <a-input v-model="model.overFlag" placeholder="请输入超载标识"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="出/入厂称重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="direction">
              <a-input v-model="model.direction" placeholder="请输入出/入厂称重"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车头照片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="headImage">
              <a-input v-model="model.headImage" placeholder="请输入车头照片"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车尾照片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tailImage">
              <a-input v-model="model.tailImage" placeholder="请输入车尾照片"  ></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'OverWeightDataForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/overweight/overWeightData/add",
          edit: "/overweight/overWeightData/edit",
          queryById: "/overweight/overWeightData/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>