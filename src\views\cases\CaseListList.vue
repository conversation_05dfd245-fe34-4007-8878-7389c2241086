<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="4" :lg="6" :md="8" :sm="24">
            <a-form-item label="站点">
              <j-dict-select-tag placeholder="请选择站点" v-model="queryParam.siteCode" dictCode="base_site,name,code" />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="车辆号牌">
              <a-input placeholder="请输入车辆号牌" v-model="queryParam.plate"></a-input>
            </a-form-item>
          </a-col>

          <template v-if="toggleSearchStatus">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="运输企业">
                <a-input placeholder="请输入运输企业名称" v-model="queryParam.ysDw"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="源头名称">
                <a-input placeholder="请输入源头名称" v-model="queryParam.ytDw"></a-input>
              </a-form-item>
            </a-col>
             <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="司机">
                <a-input placeholder="请输入司机姓名" v-model="queryParam.driverName"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button type="primary" icon="download" @click="handleExportXls('案件信息列表')">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery">
      </j-super-query>
      <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown> -->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{
            selectedRowKeys.length
        }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
        <span style="float:right;">
          <a @click="loadData()">
            <a-icon type="sync" />刷新
          </a>
          <a-divider type="vertical" />
          <a-popover title="自定义列" trigger="click" placement="leftBottom">
            <template slot="content">
              <a-checkbox-group @change="onColSettingsChange" v-model="settingColumns" :defaultValue="settingColumns">
                <a-row style="width: 400px">
                  <template v-for="(item, index) in defColumns">
                    <template v-if="item.key != 'rowIndex' && item.dataIndex != 'action'">
                      <a-col :span="12">
                        <a-checkbox :value="item.dataIndex">
                          <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                        </a-checkbox>
                      </a-col>
                    </template>
                  </template>
                </a-row>
              </a-checkbox-group>
            </template>
            <a>
              <a-icon type="setting" />设置
            </a>
          </a-popover>
        </span>
      </div>

      <a-tabs @change="handleTabs">
        <a-tab-pane key="0" tab="未结案">
          <a-table ref="table" size="middle" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
            :dataSource="dataSource" :pagination="ipagination" :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" class="j-table-force-nowrap"
            @change="handleTableChange">
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="status" slot-scope="text, record">
              <div v-if="record.isFinish == '0'">
                <span>未结案</span>
              </div>
              <div v-else>
                <span>已结案</span>
                <span v-html="record.endRiqi"></span>
              </div>
            </template>
            <span slot="action" slot-scope="text, record">
              <a @click="handleMenu(record)">目录</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多
                  <a-icon type="down" />
                </a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a @click="handleDetail(record)">详情</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="1" tab="已结案" force-render>
          <a-table ref="table" size="middle" :scroll="{ x: true }" bordered rowKey="id" :columns="columns"
            :dataSource="dataSource" :pagination="ipagination" :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" class="j-table-force-nowrap"
            @change="handleTableChange">
            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>
            <template slot="status" slot-scope="text, record">
              <div v-if="record.isFinish == '0'">
                <span>未结案</span>
              </div>
              <div v-else>
                <span>已结案</span>
                <p v-html="record.endDate"></p>
              </div>
            </template>
            <span slot="action" slot-scope="text, record">
              <a @click="handleMenu(record)">目录</a>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多
                  <a-icon type="down" />
                </a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a @click="handleDetail(record)">详情</a>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </a-tab-pane>

      </a-tabs>

    </div>

    <case-list-modal ref="modalForm" @ok="modalFormOk"></case-list-modal>
    <case-list-menu-model ref="modalFileMenu"></case-list-menu-model>
  </a-card>
</template>

<script>

import '@/assets/less/TableExpand.less'
import Vue from 'vue'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CaseListModal from './modules/CaseListModal.Style#Drawer'
import CaseListMenuModel from './modules/CaseListMenuModel'
import JEllipsis from '@/components/jeecg/JEllipsis'
export default {
  name: 'CaseListList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CaseListModal,
    CaseListMenuModel,
    JEllipsis
  },
  data() {
    let ellipsis = (v, l = 4) => (<j-ellipsis value={v} length={l} />)
    return {
      description: '案件信息列表管理页面',
      //表头
      columns: [],
      //列设置
      settingColumns: [],
      // 表头
      defColumns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: "center",
          customRender: function (t, r, index) {
            return parseInt(index) + 1;
          }
        },
        {
          title: '站点名称',
          align: "center",
          dataIndex: 'siteName'
        },
        {
          title: '案件类型',
          align: "center",
          dataIndex: 'type_dictText'
        },
        // {
        //   title:'违法行为种类',
        //   align:"center",
        //   dataIndex: 'zfType'
        // },
        {
          title: '受案时间',
          align: "center",
          dataIndex: 'acceptTime',
          sorter: true,
          sortDirections: ['descend', 'ascend', 'descend'],
          // 配置默认是倒序
          defaultSortOrder: 'descend',
        },
        // {
        //   title: '行政处罚时间',
        //   align: "center",
        //   dataIndex: 'punishTime'
        // },
        {
          title: '车牌号',
          align: "center",
          dataIndex: 'plate'
        },
        {
          title: '轴型',
          align: "center",
          dataIndex: 'axleType'
        },
        {
          title: '轴数',
          align: "center",
          dataIndex: 'axles'
        },
        {
          title: '总重',
          align: "center",
          dataIndex: 'total'
        },
        {
          title: '货物',
          align: "center",
          dataIndex: 'goods',
          customRender: (text) => ellipsis(text)
        },
        {
          title: '运输企业名称',
          align: "center",
          dataIndex: 'ysDw',
          customRender: (text) => ellipsis(text)
        },
        {
          title: '货运源头名称',
          align: "center",
          dataIndex: 'ytDw'
        },
        {
          title: '是否结案',
          align: "center",
          dataIndex: 'isFinish',
          scopedSlots: {
            customRender: "status"
          }
        },
        {
          title: '司机姓名',
          align: "center",
          dataIndex: 'driverName'
        },
        {
          title: '车主姓名',
          align: "center",
          dataIndex: 'czName'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: "center",
          fixed: "right",
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: "/cases/caseList/list?isFinish=0",
        delete: "/cases/caseList/delete",
        deleteBatch: "/cases/caseList/deleteBatch",
        exportXlsUrl: "/cases/caseList/exportXls",
        importExcelUrl: "cases/caseList/importExcel",
      },
      dictOptions: {},
      superFieldList: [],
      isCaseDefault: '0',
    }
  },
  created() {
    this.getSuperFieldList();
    this.initColumns();
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    initDictConfig() {
    },
    onColSettingsChange(checkedValues) {
      var key = this.$route.name + ":colsettings";
      Vue.ls.set(key, checkedValues, 7 * 24 * 60 * 60 * 1000)
      this.settingColumns = checkedValues;
      const cols = this.defColumns.filter(item => {
        if (item.key == 'rowIndex' || item.dataIndex == 'action') {
          return true
        }
        if (this.settingColumns.includes(item.dataIndex)) {
          return true
        }
        return false
      })
      this.columns = cols;
    },
    initColumns() {
      //权限过滤（列权限控制时打开，修改第二个参数为授权码前缀）
      //this.defColumns = colAuthFilter(this.defColumns,'testdemo:');
      var key = this.$route.name + ":colsettings";
      let colSettings = Vue.ls.get(key);
      if (colSettings == null || colSettings == undefined) {
        let allSettingColumns = [];
        this.defColumns.forEach(function (item, i, array) {
          allSettingColumns.push(item.dataIndex);
        })
        this.settingColumns = allSettingColumns;
        this.columns = this.defColumns;
      } else {
        this.settingColumns = colSettings;
        const cols = this.defColumns.filter(item => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true;
          }
          if (colSettings.includes(item.dataIndex)) {
            return true;
          }
          return false;
        })
        this.columns = cols;
      }
    },
    getSuperFieldList() {
      let fieldList = [];
      fieldList.push({ type: 'string', value: 'code', text: '案件编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'siteCode', text: '站点编码', dictCode: '' })
      fieldList.push({ type: 'string', value: 'siteName', text: '站点名称', dictCode: '' })
      fieldList.push({ type: 'int', value: 'type', text: '案件类型', dictCode: '' })
      fieldList.push({ type: 'string', value: 'form', text: '案件来源', dictCode: '' })
      fieldList.push({ type: 'string', value: 'reason', text: '案由', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfTeam', text: '执法支队名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'xcz', text: '车辆通行证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfType', text: '违法行为种类', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'acceptTime', text: '受案时间' })
      fieldList.push({ type: 'string', value: 'reasonLaw', text: '立案依据法律', dictCode: '' })
      fieldList.push({ type: 'string', value: 'reasonLawItem', text: '立案法律条款', dictCode: '' })
      fieldList.push({ type: 'string', value: 'punishLaw', text: '处罚依据法律', dictCode: '' })
      fieldList.push({ type: 'string', value: 'punishLawItem', text: '处罚法律条款', dictCode: '' })
      fieldList.push({ type: 'string', value: 'cfjdsNo', text: '处罚决定书编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'arrestLaw', text: '扣留立案依据法律', dictCode: '' })
      fieldList.push({ type: 'string', value: 'arrestLawItem', text: '扣留立案依据条款', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'punishTime', text: '行政处罚时间' })
      fieldList.push({ type: 'string', value: 'plate', text: '车牌号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'plateGua', text: '挂车号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'axleType', text: '轴型', dictCode: '' })
      fieldList.push({ type: 'string', value: 'axles', text: '轴数', dictCode: '' })
      fieldList.push({ type: 'int', value: 'total', text: '总重', dictCode: '' })
      fieldList.push({ type: 'string', value: 'goods', text: '货物', dictCode: '' })
      fieldList.push({ type: 'string', value: 'ysz', text: '运输证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'cyz', text: '从业证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'ysDw', text: '运输企业名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'ysDwJyxkz', text: '运输企业经营许可证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'ytDw', text: '货运源头单位名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'ytDwJyxkz', text: '货运源头单位许可证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfPerson1Code', text: '执法人员1编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfPerson1Name', text: '执法人员1姓名', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfPerson1Dwzw', text: '执法人员1单位及职务', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfPerson2Code', text: '执法人员2编号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfPerson2Name', text: '执法人员2姓名', dictCode: '' })
      fieldList.push({ type: 'string', value: 'zfPerson2Dwzw', text: '执法人员2单位及职务', dictCode: '' })
      fieldList.push({ type: 'int', value: 'isFinish', text: '是否结案', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'endDate', text: '结案日期' })
      fieldList.push({ type: 'string', value: 'driverName', text: '司机姓名', dictCode: '' })
      fieldList.push({ type: 'int', value: 'driverSex', text: '司机性别', dictCode: '' })
      fieldList.push({ type: 'int', value: 'driverAge', text: '司机年龄', dictCode: '' })
      fieldList.push({ type: 'string', value: 'driverIdCard', text: '司机身份证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'driverDwzw', text: '司机单位及职务', dictCode: '' })
      fieldList.push({ type: 'string', value: 'driverAddress', text: '司机住址', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dirverTel', text: '司机电话', dictCode: '' })
      fieldList.push({ type: 'string', value: 'driverJszh', text: '司机驾驶证号', dictCode: '' })
      fieldList.push({ type: 'string', value: 'driverPosetCode', text: '司机邮编', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dsrZy', text: '当事人职业', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dsrQyName', text: '当事人企业名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dsrQyLegalPerson', text: '当事人企业法人代表', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dsrQyAddress', text: '当事人企业地址', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dsrQyTel', text: '当事人企业电话', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czQyName', text: '车主企业名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czQyLegalPerson', text: '车主企业法人', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czQyAddress', text: '车主企业地址', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czQyTel', text: '车主企业电话', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czName', text: '车主姓名', dictCode: '' })
      fieldList.push({ type: 'int', value: 'czSex', text: '车主性别', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czIdCard', text: '车主身份证', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czAddress', text: '车主住址', dictCode: '' })
      fieldList.push({ type: 'string', value: 'czTel', text: '车主电话', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dataId', text: '数据编码', dictCode: '' })
      this.superFieldList = fieldList
    },
    handleTabs(key) {
      this.isCaseDefault = key;
      this.url.list = "/cases/caseList/list?isFinish=" + key;
      this.loadData();
    },
    handleMenu(record) {
      this.$refs.modalFileMenu.show(record);
      this.$refs.modalFileMenu.title = "目录列表";
      this.$refs.modalFileMenu.disableSubmit = true;
    },
    handleDetail(record) {
      this.$refs.modalForm.show(record);
      this.$refs.modalForm.title = "案件详情";
      this.$refs.modalForm.disableSubmit = true;
    },

  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>