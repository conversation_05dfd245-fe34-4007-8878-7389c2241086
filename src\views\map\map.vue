<template>
  <!-- 保持模板部分不变 -->
  <div class="map">
    <div id="map" style="height: 90vh; width: 100vw" ref="map">
      <div class="control">
        <a-radio-group :value="type" @change="handleSiteTypeChange">
          <a-radio-button value="all"> 全部 </a-radio-button>
          <a-radio-button value="site"> 站点 </a-radio-button>
          <a-radio-button value="enterprise"> 企业 </a-radio-button>
          <a-radio-button value="unattended"> 无人 </a-radio-button>
        </a-radio-group>
      </div>
      <div id="popup" class="ol-popup">
        <a href="#" id="popup-closer" class="ol-popup-closer">X</a>
        <div id="popup-content" class="popup-content"></div>
      </div>
    </div>
  </div>
</template>

<script>
import 'ol/ol.css'
import { Map, View } from 'ol'
import TileLayer from 'ol/layer/Tile'
import XYZ from 'ol/source/XYZ' // 修改1：引入XYZ源

import proj4 from 'proj4'
import { register } from 'ol/proj/proj4'
import { fromLonLat } from 'ol/proj'
import Feature from 'ol/Feature'
import Point from 'ol/geom/Point'
import { Icon, Style } from 'ol/style'
import VectorSource from 'ol/source/Vector'
import Overlay from 'ol/Overlay'
import { Vector as VectorLayer } from 'ol/layer'
import { getAction } from '@/api/manage'
import markSite from '@assets/map/marker_site.png'
import markEnterprise from '@assets/map/marker_enterprise.png'
import markUnattended from '@assets/map/marker_unattended.png'
import {filterDictTextByCache} from '@/components/dict/JDictSelectUtil'

export default {
  name: 'showMap',
  data() {
    // 保持data不变
    return {
      map: {},
      visible: false,
      type: 'all',
      marker: markSite,
      vectorSiteLayer: null,
      vectorEnterpriseLayer: null,
      vectorUnattendedLayer: null,
      siteArr: [],
      enterpriseArr: [],
      unattendedArr: [],
      popover: null,
      overlay: null,
      container: null,
      content: null,
      closer: null,
    }
  },
  mounted() {
    // 保持生命周期不变
    this.initMap()
    this.addSiteMarker()
    this.addEnterpriseMarker()
    this.addUnattendedMarker()
    this.bindClick()
  },
  watch: {
    /* 保持watch不变 */
    siteArr(newArr, oldArr) {
      var vectorSource = new VectorSource({
        features: newArr,
      })
      this.vectorSiteLayer = new VectorLayer({
        source: vectorSource,
      })
      this.map.addLayer(this.vectorSiteLayer)
    },
    enterpriseArr(newArr, oldArr) {
      var vectorSource = new VectorSource({
        features: newArr,
      })
      this.vectorEnterpriseLayer = new VectorLayer({
        source: vectorSource,
      })
      this.map.addLayer(this.vectorEnterpriseLayer)
    },
    unattendedArr(newArr, oldArr) {
      var vectorSource = new VectorSource({
        features: newArr,
      })
      this.vectorUnattendedLayer = new VectorLayer({
        source: vectorSource,
      })
      this.map.addLayer(this.vectorUnattendedLayer)
    },
  },
  methods: {
    /* 保持方法不变，仅修改initMap中的地图初始化部分 */
    handleSiteTypeChange(e) {
      this.type = e.target.value
      this.clearAllMarker()
      switch (e.target.value) {
        case 'site':
          this.addSiteMarker()
          break
        case 'enterprise':
          this.addEnterpriseMarker()
          break
        case 'unattended':
          this.addUnattendedMarker()
          break
        default:
          this.addSiteMarker()
          this.addEnterpriseMarker()
          this.addUnattendedMarker()
      }
    },

    // 初始化地图（主要修改部分）
    initMap() {
      proj4.defs('EPSG:4490', '+proj=longlat +ellps=GRS80 +no_defs')
      register(proj4)
      
      // 创建高德地图图层（修改2）
      const amapLayer = new TileLayer({
        source: new XYZ({
          url: 'https://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}',
          crossOrigin: 'anonymous'
        })
      })

      // 初始化地图（修改3：使用高德图层）
      this.map = new Map({
        target: 'map',
        layers: [amapLayer], // 只保留高德底图
        view: new View({
          center: fromLonLat([112.852022, 35.491315]), // 保持坐标转换不变
          zoom: 10
        })
      })

      // 保持覆盖层初始化不变
      this.container = document.getElementById('popup')
      this.content = document.getElementById('popup-content')
      this.closer = document.getElementById('popup-closer')
      
      this.overlay = new Overlay({
        element: this.container,
        autoPan: true,
        autoPanAnimation: {
          duration: 250,
        },
      })
      this.map.addOverlay(this.overlay)
    },

    // 以下方法保持不变
    clearAllMarker() {
      if (this.vectorSiteLayer != null) {
        this.siteArr.forEach((siteFe) => {
          this.vectorSiteLayer.getSource().removeFeature(siteFe)
        })
      }
      if (this.vectorEnterpriseLayer != null) {
        this.enterpriseArr.forEach((siteFe) => {
          this.vectorEnterpriseLayer.getSource().removeFeature(siteFe)
        })
      }
      if (this.vectorUnattendedLayer != null) {
        this.unattendedArr.forEach((siteFe) => {
          this.vectorUnattendedLayer.getSource().removeFeature(siteFe)
        })
      }
    },

    addSiteMarker() {
      getAction('/system/baseSite/listAll').then((res) => {
        if (res.success) {
          var siteList = res.result
          const arr = []
          siteList.forEach((site) => {
            var iconFeature = new Feature({
              geometry: new Point(fromLonLat([site.longitude, site.latitude])),
              name: 'site',
              population: 4000,
              rainfall: 500,
            })
            iconFeature.data = site
            var iconStyle = new Style({
              image: new Icon({
                anchor: [0.5, 1],
                crossOrigin: 'anonymous',
                src: markSite,
                scale: 1,
              }),
            })
            iconFeature.setStyle(iconStyle)
            arr.push(iconFeature)
          })
          this.siteArr = arr
        }
      })
    },

    addEnterpriseMarker() {
      getAction('/system/baseEnterprise/listAll').then((res) => {
        if (res.success) {
          var enterpriseList = res.result
          const arr = []
          enterpriseList.forEach((site) => {
            if (site.longitude != null && site.latitude != null) {
              var iconFeature = new Feature({
                geometry: new Point(fromLonLat([site.longitude, site.latitude])),
                name: 'enterprise',
                population: 300,
                rainfall: 500,
              })
              iconFeature.data = site
              var iconStyle = new Style({
                image: new Icon({
                  anchor: [0.5, 1],
                  crossOrigin: 'anonymous',
                  src: markEnterprise,
                  scale: 1,
                }),
              })
              iconFeature.setStyle(iconStyle)
              arr.push(iconFeature)
            }
          })
          this.enterpriseArr = arr
        }
      })
    },

    addUnattendedMarker() {
      getAction('/system/baseUnattended/listAll').then((res) => {
        if (res.success) {
          var unattendedList = res.result
          var arr = []
          unattendedList.forEach((site) => {
            if (site.latitude != null && site.longitude != null) {
              var iconFeature = new Feature({
                geometry: new Point(fromLonLat([site.longitude, site.latitude])),
                name: 'unattended',
                population: 4000,
                rainfall: 500,
              })
              iconFeature.data = site
              var iconStyle = new Style({
                image: new Icon({
                  anchor: [0.5, 1],
                  crossOrigin: 'anonymous',
                  src: markUnattended,
                  scale: 1,
                }),
              })
              iconFeature.setStyle(iconStyle)
              arr.push(iconFeature)
            }
          })
          this.unattendedArr = arr
        }
      })
    },

    bindClick() {
      let _that = this
      this.map.on('singleclick', (evt) => {
        this.clickMap(evt)
      })
      this.closer.onclick = function () {
        _that.overlay.setPosition(undefined)
        _that.closer.blur()
        return false
      }
    },
    convertSiteZt(siteZt){
      console.log("aaaaaaaaaa",siteZt);
      switch(siteZt){
          case '1':
            return '规划'
          case '2':
            return '在建'
          case '3':
            return '运行'
          case '4':
            return '停用'
          case '5':
            return '改造'
      }
    },
    clickMap(evt) {
      var feature = this.map.forEachFeatureAtPixel(evt.pixel, function (feature) {
        return feature
      })
      if (feature) {
        var coordinates = feature.getGeometry().getCoordinates()
        var attr = feature.getProperties()
        let site = feature.data
        if (attr.name == 'site') {
          getAction("/statistics/statisticsSite/todayInfo",{siteCode: site.code}).then(res =>{
            if(res.success){
              this.content.innerHTML = `
                <p>站点名称:  ${site.name}  </p>
                <p>站点状态:  ${this.convertSiteZt(site.siteZt + "")}  </p>
                <p>今日过车数:  ${res.result.todayCarlTotal}  </p>
                <p>今日超载数:  ${res.result.todayOverCarTotal}  </p>
              `
            }
          });
        } else if (attr.name == 'enterprise') {
          getAction("/statistics/statisticsEnterprise/todayInfo",{siteCode: site.code}).then(res => {
            this.content.innerHTML = `
              <p>企业名称:  ${site.enterpriseName}  </p>
              <p>企业地址:  ${site.address}  </p>
              <p>今日过车数:  ${res.result.todayCarlTotal}  </p>
              <p>今日超载数:  ${res.result.todayOverCarTotal}  </p>
            `
          });
        } else if(attr.name == 'unattended'){
          getAction("/statistics/statisticsUnattended/todayInfo",{siteCode: site.code}).then(res => {
            this.content.innerHTML = `
              <p>站点名称:  ${site.name}  </p>
              <p>站点状态:  ${filterDictTextByCache("siteZt",site.siteZt)} </p>
              <p>今日过车数:  ${res.result.todayCarlTotal}  </p>
              <p>今日超载数:  ${res.result.todayOverCarTotal}  </p>
            `
          });
        }
        this.overlay.setPosition(coordinates)
      } else {
        this.overlay.setPosition(undefined)
        this.closer.blur()
      }
    }
  }
}
</script>

<!-- 保持样式不变 -->
<style>
.map {
  position: relative;
}
.control {
  position: absolute;
  height: 50px;
  width: 300px;
  z-index: 9999;
  bottom: 50px;
  right: 10px;
}
.ol-popup {
  position: relative;
  background-color: white;
  -webkit-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
  filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
  padding: 15px;
  border-radius: 10px;
  border: 1px solid #cccccc;
  top: -255px;
  left: -200px;
  z-index: 999;
}
.ol-popup::after {
  width: 0;
  height: 0;
  content: '';
  border-width: 11px;
  border-color: rgb(253, 253, 253) transparent transparent transparent;
  border-style: dashed dashed solid dashed;
  position: absolute;
  bottom: -22px;
  right: 220px;
  z-index: 99;
}
.popup-content {
  width: 400px;
}
.ol-popup-closer {
  text-decoration: none;
  position: absolute;
  top: 2px;
  right: 8px;
}
</style>