<template>
  <div>
    <a-card :bordered="false">
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :xl="4" :lg="7" :md="8" :sm="24">
              <a-form-item label="县区">
                <j-area-linkage type="cascader" v-model="queryParam.distCode" placeholder="请选择省市区" @change="distChange" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="时间段">
                <j-date-range style="width:100%" @change="dateRangeChange" :timeValue="timeValue"
                  :defaultValue="defalutValue"></j-date-range>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
                </a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" style="margin-top:10px;">
      <div class="no-print" style="text-align: right">
        <a-button v-print="'#printContent'" ghost type="primary">打印</a-button>
      </div>
      <a-row ref="print" id="printContent" class="abcdefg">
        <bar-multid title="站点数据统计分析" :height="height" :dataSource="countSource" :fields="fields" />
      </a-row>
    </a-card>
  </div>
</template>
<script>
import BarMultid from '@/components/chart/BarMultid'
import ACol from 'ant-design-vue/es/grid/Col'
import JYearPicker from '@/components/jeecg/JYearPicker'
import JMonthPicker from '@/components/jeecg/JMonthPicker'
import { getAction } from '@/api/manage'
import moment from 'moment'
import JDateRange from '@/components/jeecg/JDateRange'
export default {
  name: 'dist',
  components: {
    ACol,
    BarMultid,
    JYearPicker,
    JMonthPicker,
    JDateRange
  },
  data() {
    return {
      description: '县区站点超载分析',
      queryParam: {
      },
      countSource: [
        { type: '过车数', '上孔': 0, '换马桥': 0, '狸猫泉': 0, '夺火': 0, '寺庄': 0, '凤凰山': 0 },
        { type: '超载数', '上孔': 0, '换马桥': 0, '狸猫泉': 0, '夺火': 0, '寺庄': 0, '凤凰山': 0 }
      ],
      height: 500,
      date: [moment().startOf("month").format('YYYY-MM-DD HH:mm:ss'), moment().endOf("month").format('YYYY-MM-DD HH:mm:ss')],
      url: {
        getDistSite: "/statistics/statisticsSite/getSite"
      },
      fields: ['上孔', '换马桥', '狸猫泉', '夺火', '寺庄', '凤凰山'],
      timeValue: [moment().startOf('year'), moment().endOf('day')],
      defalutValue: [moment().startOf('year'), moment().endOf('day')],
      queryParam: {
        startTime: moment().startOf('year').format("YYYY-MM-DD 00:00:00"),
        endTime: moment().endOf('day').format("YYYY-MM-DD 23:59:59")
      }
    }
  },
  created() {
    this.loadDate(this.url.getDistSite);
  },
  methods: {
    searchQuery() {
      this.loadDate(this.url.getDistSite);
    },
    searchReset() {
      this.queryParam = {}
      this.timeValue = [moment().startOf('year'), moment().endOf('day')];
      this.queryParam.startTime = moment().startOf('year').format("YYYY-01-01 00:00:00");
      this.queryParam.endTime = moment().endOf('day').format("YYYY-MM-DD 23:59:59");
      this.loadDate(this.url.getDistSite);

    },
    loadDate(url) {
      getAction(url, this.queryParam, "get").then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            this.convertDataSource(res.result);
          } else {
            this.$message.warning("未查询出结果");
          }
        } else {
          var that = this;
          that.$message.error(res.message);
        }
      });
    },
    convertDataSource(data) {
      if (data.length != 0) {
        var fieldsArr = [];
        var numArr = [];
        var ltOneArr = [];
        for (let i = 0; i < data.length; i++) {
          fieldsArr.push(data[i].name)
          numArr.push(data[i].num);
          ltOneArr.push(data[i].gt_one);
        }
        this.fields = [];
        this.fields = fieldsArr;
        var numData = { type: "过车数" };
        var overData = { type: "超载数" };
        for (let i = 0; i < fieldsArr.length; i++) {
          numData[fieldsArr[i]] = numArr[i];
          overData[fieldsArr[i]] = ltOneArr[i];
        }
        this.countSource = [];
        this.countSource.push(numData);
        this.countSource.push(overData);
        console.log(this.countSource);
      }


    },
    distChange(data) {
      this.dist = "base_site,name,code,dist_code=" + data;
      this.queryParam.siteCode = null;
    },
    jdictChange(data) {
      this.queryParam.siteCode = data;
    },
    dateRangeChange(dates, dateStr) {
      this.timeValue = dates;
      this.queryParam.startTime = dateStr[0]
      this.queryParam.endTime = dateStr[1]
    },
  }
}
</script>
<style scoped>
.ant-card-body .table-operator {
  margin-bottom: 18px;
}

.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

.anty-row-operator button {
  margin: 0 5px
}

.ant-btn-danger {
  background-color: #ffffff
}

.ant-modal-cust-warp {
  height: 100%
}

.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}

.statistic {
  padding: 0px !important;
  margin-top: 50px;
}

.statistic h4 {
  margin-bottom: 20px;
  text-align: center !important;
  font-size: 24px !important;
  ;
}

.statistic #canvas_1 {
  width: 100% !important;
}
</style>
