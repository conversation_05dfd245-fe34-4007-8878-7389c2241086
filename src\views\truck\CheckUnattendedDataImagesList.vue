<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="4" :lg="6" :md="8" :sm="24">
            <a-form-item label="站点">
              <j-dict-select-tag
                placeholder="请选择站点"
                v-model="queryParam.siteCode"
                dict="base_unattended,name,code"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="车辆号牌">
              <a-input placeholder="请输入车辆号牌" v-model="queryParam.vehicleNo"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="数据类型">
              <j-dict-select-tag placeholder="请选择图片类型" v-model="queryParam.dataType" dictCode="car_image_type" />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="7" :lg="11" :md="18" :sm="24">
              <a-form-item label="检测时间">
                <a-range-picker
                  :ranges="{
                    当天: [moment(), moment()],
                    当月: [moment().startOf('month'), moment().endOf('month')],
                    当年: [moment().startOf('year'), moment().endOf('year')],
                  }"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  @change="onChangeTime"
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
      <a-button type="primary" icon="download" @click="handleExportXls('无人站点过车图片表')">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query
        :fieldList="superFieldList"
        ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"
      ></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <check-unattended-data-images-modal ref="modalForm" @ok="modalFormOk"></check-unattended-data-images-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CheckUnattendedDataImagesModal from './modules/unattended/CheckUnattendedDataImagesModal'
import moment from 'moment'

export default {
  name: 'CheckUnattendedDataImagesList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CheckUnattendedDataImagesModal,
  },
  data() {
    return {
      description: '无人站点过车图片表管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '关联ID',
          align: 'center',
          dataIndex: 'checkDataId',
        },
        {
          title: '站点名称',
          align: 'center',
          dataIndex: 'siteCode_dictText',
        },
        {
          title: '检测时间',
          align: 'center',
          dataIndex: 'checkTime',
        },
        {
          title: '车辆号牌',
          align: 'center',
          dataIndex: 'vehicleNo',
        },
        {
          title: '文件名称',
          align: 'center',
          dataIndex: 'fileName',
        },
        {
          title: '数据类型',
          align: 'center',
          dataIndex: 'dataType_dictText',
        },
        {
          title: '文件大小',
          align: 'center',
          dataIndex: 'dataFileSize',
        },
        {
          title: '文件地址',
          align: 'center',
          dataIndex: 'dataUrl',
          scopedSlots: { customRender: 'imgSlot' },
        },
        {
          title: '创建日期',
          align: 'center',
          sorter: true,
          dataIndex: 'createTime',
        },
        // {
        //   title: '操作',
        //   dataIndex: 'action',
        //   align:"center",
        //   fixed:"right",
        //   width:147,
        //   scopedSlots: { customRender: 'action' }
        // }
      ],
      url: {
        list: '/truck/checkUnattendedDataImages/list',
        delete: '/truck/checkUnattendedDataImages/delete',
        deleteBatch: '/truck/checkUnattendedDataImages/deleteBatch',
        exportXlsUrl: '/truck/checkUnattendedDataImages/exportXls',
        importExcelUrl: 'truck/checkUnattendedDataImages/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    moment,
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'checkDataId', text: '关联检测信息的数据唯一标识uniqueId', dictCode: '' })
      fieldList.push({ type: 'string', value: 'siteCode', text: '站点编号' })
      fieldList.push({ type: 'date', value: 'checkTime', text: '检测时间', dictCode: '' })
      fieldList.push({ type: 'string', value: 'vehicleNo', text: '车辆号牌', dictCode: '' })
      fieldList.push({ type: 'string', value: 'fileName', text: '文件名称', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dataType', text: '数据类型', dictCode: '' })
      fieldList.push({ type: 'int', value: 'dataFileSize', text: '文件大小', dictCode: '' })
      fieldList.push({ type: 'string', value: 'dataUrl', text: '文件地址', dictCode: '' })
      fieldList.push({ type: 'datetime', value: 'createTime', text: '创建日期' })
      this.superFieldList = fieldList
    },
    onChangeTime(dates, dateStrings) {
      this.queryParam.checkTime_begin = dateStrings[0]
      this.queryParam.checkTime_end = dateStrings[1]
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>