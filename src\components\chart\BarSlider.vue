<template>
  <div :style="{ padding: '0 0 32px 32px' }">
    <h4 :style="{ marginBottom: '20px' }">{{ title }}</h4>
    <v-chart :forceFit="true" :height="height" :data="dv" :scale="scale" :padding="padding">
      <v-tooltip />
      <v-axis />
      <v-bar position="x*y" />
    </v-chart>
    <v-plugin>
      <v-slider width="auto" :height="26" container='viser-slider-1' :start="start" :end="end" :data="originDv"
        x-axis="x" y-axis="y" :scales="scale1" :backgroundChart="bg" :onChange="onChange" />
    </v-plugin>
  </div>
</template>

<script>
import { triggerWindowResizeEvent } from '@/utils/util'
import DataSet from '@antv/data-set'

export default {
  name: 'BarSlider',
  props: {
    dataSource: {
      type: Array,
      required: true
    },
    yaxisText: {
      type: String,
      default: '超载数'
    },
    title: {
      type: String,
      default: ''
    },
    height: {
      type: Number,
      default: 254
    },
    originDv: {
      type: Array,
      default: [],
    },

  },
  data() {
    return {
      padding: ['auto', 'auto', '40', '50'],
      data: [],
      start: "",
      end: "",
      startIndex: 0,
      endIndex: 0,
      dv: {},
      pad: [40, 40, 40, 80],
      scale1: [
        {
          dataKey: "x",
          type: "string",
          // tickCount: 3,
          mask: "站点"
        },
        {
          dataKey: "y",
          alias: "超载数"
        },
      ],
      bg: { type: 'line' },
    }
  },
  created() {
    this.start = this.originDv[0].x;
    this.end = this.originDv[10].x;
  },
  computed: {
    scale() {
      return [{
        dataKey: 'y',
        alias: this.yaxisText
      }]
    }
  },
  methods: {
    onChange(_ref) {
      var startValue = _ref.startValue, endValue = _ref.endValue;
      this.start = startValue;
      this.end = endValue;
      this.dv = this.getData().dv;
    },
    getData() {
      const { originDv, start, end } = this;
      var startIndex;
      var endIndex;
      originDv.forEach(function (item, index) {
        if (item.x == start) {
          startIndex = index;
        }
        if (item.x == end) {
          endIndex = index;
        }
      });
      const ds = new DataSet({
        state: {
          start: startIndex,
          end: endIndex
        }
      });
      const dv = ds.createView("origin").source(originDv);
      dv.transform({
        type: "filter",
        callback: function callback(obj, index) {
          return index >= ds.state.start && index <= ds.state.end;
        }
      });
      return { dv, ds };
    }
  },
  mounted() {
    triggerWindowResizeEvent()
    const { dv, ds } = this.getData();
    this.$data.dv = dv;
    this.$data.start = ds.state.start;
    this.$data.end = ds.state.end;
  }
}
</script>